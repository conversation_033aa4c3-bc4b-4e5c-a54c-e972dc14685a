// android-junk-code插件配置
androidJunkCode {
    variantConfig {
        playstoreRelease {
            packageBase = "com.lanxtech.plantocr"  //生成java类根包名
            packageCount = 30 //生成包数量
            activityCountPerPackage = 6 //每个包下生成Activity类数量
            excludeActivityJavaFile = false
            //是否排除生成Activity的Java文件,默认false(layout和写入AndroidManifest.xml还会执行)，主要用于处理类似神策全埋点编译过慢问题
            otherCountPerPackage = 50  //每个包下生成其它类的数量
            methodCountPerClass = 22  //每个类下生成方法数量
            resPrefix = "lanxtech_"  //生成的layout、drawable、string等资源名前缀
            drawableCount = 301  //生成drawable资源数量
            stringCount = 303  //生成string数量
        }
    }
}

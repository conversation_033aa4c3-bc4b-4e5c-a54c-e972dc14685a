<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.dicernszh.botany">

    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="32"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"
        tools:node="remove"
        />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"
        tools:node="remove"
        />

    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"
                     tools:node="remove"
    />

    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
                     tools:node="remove"
                     tools:ignore="QueryAllPackagesPermission"/>


    <uses-permission
        android:name="android.permission.SYSTEM_ALERT_WINDOW"
        tools:node="remove" />

    <application
        android:name=".ZJZApplication"
        android:allowBackup="false"
        android:icon="${icon}"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:ignore="GoogleAppIndexingWarning"
        tools:replace="android:label">
        <activity
            android:exported="true"
            android:name=".SplashPreActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".CameraActivity"
            android:configChanges="screenLayout|keyboardHidden"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">

        </activity>

        <activity
            android:name=".v2.MainActivity"
            android:screenOrientation="portrait" />
        <activity android:name=".v2.fragment.mine.AboutAct" />
        <activity android:name=".v2.fragment.mine.AboutUsActivity" />
        <activity android:name=".v2.fragment.mine.FeedbackAct" />
        <activity android:name=".v2.fragment.mine.FeedbackActivity" />
        <activity android:name=".v2.fragment.mine.PrivacyAct" />
        <activity android:name=".v2.fragment.mine.PrivacyActivity" />
        <activity android:name=".Image2TxtResultAct" />
        <activity android:name=".Image2ObjResultAct" />
        <activity android:name=".ResultHistoryAct" />
        <activity
            android:name=".WebPage"
            android:screenOrientation="portrait" />
        <activity
            android:name=".SplashActivity"
            android:screenOrientation="portrait" />

        <provider
            android:name=".MyFileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths_public" />
        </provider>

<!--        <provider-->
<!--            android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"-->
<!--            android:authorities="${applicationId}.TTMultiProvider"-->
<!--            android:exported="false" />-->

<!--        <provider-->
<!--            android:name="com.bytedance.sdk.openadsdk.TTFileProvider"-->
<!--            android:authorities="${applicationId}.TTFileProvider"-->
<!--            android:exported="false"-->
<!--            android:grantUriPermissions="true">-->
<!--            <meta-data-->
<!--                android:name="android.support.FILE_PROVIDER_PATHS"-->
<!--                android:resource="@xml/tt_file_paths" />-->
<!--        </provider>-->


        <meta-data
            android:name="design_width_in_dp"
            android:value="375" />
    </application>

</manifest>
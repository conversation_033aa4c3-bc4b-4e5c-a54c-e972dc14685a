package com.dicernszh.botany;

import com.dicernszh.botany.model.PicSize;

import java.util.ArrayList;
import java.util.List;

public class Const {
    public static List<PicSize> picSizeList = new ArrayList<>();
    public static List<PicSize> allPicList = new ArrayList<>();

    static {
        picSizeList.add(new PicSize(295, 413, "一寸", "适用：学生证、简历照"));
        picSizeList.add(new PicSize(260, 378, "小一寸", "适用：驾照、报名照"));
        picSizeList.add(new PicSize(413, 550, "二寸", "适用：通行证照"));
        picSizeList.add(new PicSize(390, 550, "小二寸", "适用：职业考证照"));
        picSizeList.add(new PicSize(531, 708, "研究生考试（4：3）", "适用：职业考证照"));
    }

    static {
        allPicList.add(new PicSize(295, 413, "一寸", "适用：学生证、简历照"));
        allPicList.add(new PicSize(260, 378, "小一寸", "适用：驾照、报名照"));
        allPicList.add(new PicSize(413, 550, "二寸", "适用：通行证照"));
        allPicList.add(new PicSize(390, 550, "小二寸", "适用：职业考证照"));
        allPicList.add(new PicSize(531, 708, "研究生考试（4：3）", "适用：职业考证照"));
        allPicList.add(new PicSize(295, 413, "2019初级会计资格", "适用：职业考证照"));
        allPicList.add(new PicSize(390, 567, "硕士研究生考试", "适用：职业考证照"));
        allPicList.add(new PicSize(413, 513, "国家公务员考试", "适用：职业考证照"));
        allPicList.add(new PicSize(480, 640, "高考报名", "适用：职业考证照"));
        allPicList.add(new PicSize(295, 413, "简历", "适用：职业考证照"));
        allPicList.add(new PicSize(390, 567, "普通话水平测试", "适用：职业考证照"));
        allPicList.add(new PicSize(144, 192, "普通话水平测试", "适用：职业考证照"));
    }

}

package com.dicernszh.botany

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.content.ContextCompat.startActivity
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.dicernszh.botany.databinding.FragmnetHistoryLayoutBinding
import com.dicernszh.botany.databinding.ItemNewHistoryBinding
import com.dicernszh.botany.model.HistoryItem

class HistoryFragment : Fragment() {
    companion object {
        val HISTORY_LIST = mutableListOf<HistoryItem>(
            HistoryItem(
                R.drawable.icon_cell1,
                R.string.plant_recognition,
                Image2TxtResultAct.RESULT_TYPE_PLANT
            ),
            HistoryItem(
                R.drawable.icon_cell2,
                R.string.animal_recognition,
                Image2TxtResultAct.RESULT_TYPE_ANIMAL
            ), HistoryItem(
                R.drawable.icon_cell3,
                R.string.flower_recognition,
                Image2TxtResultAct.RESULT_TYPE_FLOWER
            ), HistoryItem(
                R.drawable.icon_cell4,
                R.string.fruit_veg_recognition,
                Image2TxtResultAct.RESULT_TYPE_FRUIT
            ), HistoryItem(
                R.drawable.icon_cell5,
                R.string.logo_recognition,
                Image2TxtResultAct.RESULT_TYPE_LOGO
            ), HistoryItem(
                R.drawable.icon_cell6,
                R.string.dish_recognition,
                Image2TxtResultAct.RESULT_TYPE_DISH
            ), HistoryItem(
                R.drawable.icon_cell7,
                R.string.landmark_recognition,
                Image2TxtResultAct.RESULT_TYPE_LAND
            ), HistoryItem(
                R.drawable.icon_cell8,
                R.string.currency_recognition,
                Image2TxtResultAct.RESULT_TYPE_MMONEY
            ), HistoryItem(
                R.drawable.icon_cell9,
                R.string.product_recognition,
                Image2TxtResultAct.RESULT_TYPE_GOOD
            ), HistoryItem(
                R.drawable.icon_cell10,
                R.string.text_recognition,
                Image2TxtResultAct.RESULT_TYPE_NET_WORD
            )
        )
    }
    
    private var _binding: FragmnetHistoryLayoutBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmnetHistoryLayoutBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }

    private fun initView() {
        binding.rvList.layoutManager = GridLayoutManager(context, 2)
        binding.rvList.adapter = HistoryAdapter(HISTORY_LIST)
    }

    class HistoryAdapter(var list: List<HistoryItem>) : RecyclerView.Adapter<HistoryHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HistoryHolder {
            val binding = ItemNewHistoryBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
            return HistoryHolder(binding)
        }

        override fun onBindViewHolder(holder: HistoryHolder, position: Int) {
            holder.setData(HISTORY_LIST[position])
        }

        override fun getItemCount(): Int {
            return HISTORY_LIST.size
        }
    }

    class HistoryHolder(private val binding: ItemNewHistoryBinding) : RecyclerView.ViewHolder(binding.root) {
        fun setData(item: HistoryItem) {
            Glide.with(binding.root)
                .load(ContextCompat.getDrawable(binding.root.context, item.icon))
                .into(binding.ivCover)
                
            binding.tvTitle.text = binding.root.context.getString(item.nameResId)
            
            binding.root.setOnClickListener {
                startActivity(
                    it.context, 
                    Intent(it.context, ResultHistoryAct::class.java).apply {
                        putExtra(ResultHistoryAct.RESULT_TYPE, item.type)
                        putExtra(ResultHistoryAct.RESULT_TITLE, it.context.getString(item.nameResId))
                    }, 
                    null
                )
            }
        }
    }
}
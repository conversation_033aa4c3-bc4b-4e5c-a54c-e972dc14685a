package com.dicernszh.botany

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.PermissionUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ToastUtils
import com.dicernszh.botany.CameraActivity.DATA_KEY_DATA
import com.dicernszh.botany.CameraActivity.DATA_KEY_TYPE
import com.dicernszh.botany.Image2ObjResultAct.Companion.RESULT_TYPE_GENERIC
import com.dicernszh.botany.Image2ObjResultAct.Companion.RESULT_TYPE_GENERIC_LOCAL
import com.dicernszh.botany.databinding.FragmentHomeLayoutBinding
import com.dicernszh.botany.model.ImageDetectData
import com.dicernszh.botany.model.db.ResultHistoryDao
import com.dicernszh.botany.model.db.ResultHistoryTableBean
import com.dicernszh.botany.net.DetectObjBack
import com.dicernszh.botany.net.DetectObjTask
import com.dicernszh.botany.net.Url
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.qmuiteam.qmui.widget.dialog.QMUIDialog.MessageDialogBuilder
import com.qmuiteam.qmui.widget.dialog.QMUIDialogAction
import rxhttp.wrapper.utils.GsonUtil

class HomeFragment : Fragment() {
    var resultDao: ResultHistoryDao? = null

    private lateinit var storagePermissionLauncher: ActivityResultLauncher<String>
    private lateinit var mediaImagesPermissionLauncher: ActivityResultLauncher<Array<String>>
    
    // 添加ViewBinding
    private var _binding: FragmentHomeLayoutBinding? = null
    private val binding get() = _binding!!

    private fun isStoragePermissionGranted(): Boolean {
        return if (Build.VERSION.SDK_INT < 33) {
            PermissionUtils.isGranted(Manifest.permission.READ_EXTERNAL_STORAGE)
        } else {
            PermissionUtils.isGranted(Manifest.permission.READ_MEDIA_IMAGES)
        }
    }

    private val requestCameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            currentCameraCallback?.invoke()
        } else {
            ToastUtils.showLong(getString(R.string.permission_camera_denied))
        }
    }

    // Store callback temporarily
    private var currentCameraCallback: (() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        resultDao = ResultHistoryDao(activity)
        initPermissionLaunchers()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeLayoutBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun scanLocal(filePath: String?) {
        DetectObjTask<ImageDetectData>(requireActivity(), filePath, com.dicernszh.botany.net.Url.getGeneral_basic()).start(
            object : DetectObjBack<ImageDetectData> {
                override fun onSuccess(data: ImageDetectData?) {
                    var keyword: String = getString(R.string.recognition_result)

                    if (data?.result != null && data.result!!.isNotEmpty()) {
                        keyword = data.result!![0].keyword!!
                    }
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = keyword
                            type = RESULT_TYPE_GENERIC_LOCAL
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    startActivity(
                        Intent(
                            activity,
                            Image2ObjResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2ObjResultAct.RESULT_TYPE_GENERIC_LOCAL
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, filePath)
                            putExtra(Image2ObjResultAct.RESULT_DATA, data)
                        })
                }

                override fun onFail(msg: String?) {
                    // Could add error handling here
                }
            },
            ImageDetectData::class.java
        )
    }

    private fun openAlbum() {
        PictureSelector.create(this)
            .openGallery(SelectMimeType.ofImage()).setMaxSelectNum(1).isDisplayCamera(false)
            .isPreviewVideo(false)
            .setImageEngine(com.dicernszh.botany.utils.GlideEngine.createGlideEngine()).forResult(object : OnResultCallbackListener<LocalMedia> {
                override fun onResult(result: ArrayList<LocalMedia>?) {
                    scanLocal(result?.get(0)?.realPath)
                }
                override fun onCancel() {
                    // No action needed
                }
            })
    }

    private fun initPermissionLaunchers() {
        storagePermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            if (isGranted) {
                openAlbum()
            } else {
                ToastUtils.showLong(getString(R.string.permission_storage_denied))
            }
        }

        mediaImagesPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            val allGranted = permissions.entries.all { it.value }
            if (allGranted) {
                openAlbum()
            } else {
                ToastUtils.showLong(getString(R.string.permission_storage_denied))
            }
        }
    }

    private fun chooseImage() {
        if (isStoragePermissionGranted()) {
            openAlbum()
        } else {
            val permsTips = if (Build.VERSION.SDK_INT < 33) {
                getString(R.string.permission_storage_rationale)
            } else {
                getString(R.string.permission_media_rationale)
            }

            MessageDialogBuilder(activity).setMessage(permsTips).addAction(
                QMUIDialogAction( getString(R.string.cancel)
                ) { dialog, index -> dialog.dismiss() }
            ).addAction(
                QMUIDialogAction( getString(R.string.allow)
                ) { dialog, index ->
                    dialog.dismiss()
                    applyStoragePermissions()
                }
            ).create().show()
        }
    }

    private fun applyStoragePermissions() {
        if (Build.VERSION.SDK_INT < 33) {
            storagePermissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
        } else {
            mediaImagesPermissionLauncher.launch(
                arrayOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO
                )
            )
        }
    }

    private fun openCamera(callback: () -> Unit) {
        if (PermissionUtils.isGranted(Manifest.permission.CAMERA)) {
            callback()
        } else {
            MessageDialogBuilder(activity).setMessage(getString(R.string.permission_camera_denied))
                .addAction(
                    QMUIDialogAction( getString(R.string.cancel)
                    ) { dialog, index -> dialog.dismiss() }
                ).addAction(
                    QMUIDialogAction( getString(R.string.confirm)
                    ) { dialog, index ->
                        dialog.dismiss()
                        val reject = SPUtils.getInstance().getBoolean("reject")
                        if (!reject) {
                            currentCameraCallback = callback
                            requestCameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                        } else {
                            AppUtils.launchAppDetailsSettings()
                        }
                    }
                ).create().show()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        com.dicernszh.botany.ad.AdSdk.getInstance().showBanner(activity, binding.adContainer)
        
        binding.ivLocal.setOnClickListener {
            chooseImage()
        }
        
        binding.ivScan.setOnClickListener {
            openCamera {
                activity?.startActivity(Intent(activity, CameraActivity::class.java).apply {
                    putExtra(DATA_KEY_TYPE, RESULT_TYPE_GENERIC)
                    putExtra(DATA_KEY_DATA, com.dicernszh.botany.net.Url.getGeneral_basic())
                })
            }
        }
        
        binding.ivCell1.setOnClickListener {
            openCamera {
                activity?.startActivity(Intent(activity, CameraActivity::class.java).apply {
                    putExtra(DATA_KEY_TYPE, Image2TxtResultAct.RESULT_TYPE_PLANT)
                    putExtra(
                        DATA_KEY_DATA,
                        com.dicernszh.botany.model.Image2Txt(
                            Url.plant,
                            getString(R.string.plant_recognition)
                        )
                    )
                })
            }
        }
        
        binding.ivCell2.setOnClickListener {
            openCamera {
                activity?.startActivity(Intent(activity, CameraActivity::class.java).apply {
                    putExtra(DATA_KEY_TYPE, Image2TxtResultAct.RESULT_TYPE_ANIMAL)
                    putExtra(
                        DATA_KEY_DATA,
                        com.dicernszh.botany.model.Image2Txt(
                            Url.animal,
                            getString(R.string.animal_recognition)
                        )
                    )
                })
            }
        }
        
        binding.ivCell3.setOnClickListener {
            openCamera {
                activity?.startActivity(Intent(activity, CameraActivity::class.java).apply {
                    putExtra(DATA_KEY_TYPE, Image2TxtResultAct.RESULT_TYPE_FLOWER)
                    putExtra(
                        DATA_KEY_DATA,
                        com.dicernszh.botany.model.Image2Txt(
                            Url.plant,
                            getString(R.string.flower_recognition)
                        )
                    )
                })
            }
        }
        
        binding.ivCell4.setOnClickListener {
            openCamera {
                activity?.startActivity(Intent(activity, CameraActivity::class.java).apply {
                    putExtra(DATA_KEY_TYPE, Image2TxtResultAct.RESULT_TYPE_FRUIT)
                    putExtra(
                        DATA_KEY_DATA,
                        com.dicernszh.botany.model.Image2Txt(
                            Url.ingredient,
                            getString(R.string.fruit_veg_recognition)
                        )
                    )
                })
            }
        }
        
        binding.ivCell5.setOnClickListener {
            openCamera {
                activity?.startActivity(Intent(activity, CameraActivity::class.java).apply {
                    putExtra(DATA_KEY_TYPE, Image2TxtResultAct.RESULT_TYPE_LOGO)
                    putExtra(
                        DATA_KEY_DATA,
                        com.dicernszh.botany.model.Image2Txt(
                            Url.logo,
                            getString(R.string.logo_recognition)
                        )
                    )
                })
            }
        }
        
        binding.ivCell6.setOnClickListener {
            openCamera {
                activity?.startActivity(Intent(activity, CameraActivity::class.java).apply {
                    putExtra(DATA_KEY_TYPE, Image2TxtResultAct.RESULT_TYPE_DISH)
                    putExtra(
                        DATA_KEY_DATA,
                        com.dicernszh.botany.model.Image2Txt(
                            Url.dish,
                            getString(R.string.dish_recognition)
                        )
                    )
                })
            }
        }
        
        binding.ivCell7.setOnClickListener {
            openCamera {
                activity?.startActivity(Intent(activity, CameraActivity::class.java).apply {
                    putExtra(DATA_KEY_TYPE, Image2TxtResultAct.RESULT_TYPE_LAND)
                    putExtra(
                        DATA_KEY_DATA,
                        com.dicernszh.botany.model.Image2Txt(
                            Url.landmark,
                            getString(R.string.landmark_recognition)
                        )
                    )
                })
            }
        }
        
        binding.ivCell8.setOnClickListener {
            openCamera {
                activity?.startActivity(Intent(activity, CameraActivity::class.java).apply {
                    putExtra(DATA_KEY_TYPE, Image2TxtResultAct.RESULT_TYPE_MMONEY)
                    putExtra(
                        DATA_KEY_DATA,
                        com.dicernszh.botany.model.Image2Txt(
                            Url.currency,
                            getString(R.string.currency_recognition)
                        )
                    )
                })
            }
        }
        
        binding.ivCell9.setOnClickListener {
            openCamera {
                activity?.startActivity(Intent(activity, CameraActivity::class.java).apply {
                    putExtra(DATA_KEY_TYPE, Image2TxtResultAct.RESULT_TYPE_GOOD)
                    putExtra(
                        DATA_KEY_DATA,
                        com.dicernszh.botany.model.Image2Txt(
                            Url.detectImage,
                            getString(R.string.product_recognition)
                        )
                    )
                })
            }
        }
        
        binding.ivCell10.setOnClickListener {
            openCamera {
                activity?.startActivity(Intent(activity, CameraActivity::class.java).apply {
                    putExtra(DATA_KEY_TYPE, Image2TxtResultAct.RESULT_TYPE_NET_WORD)
                    putExtra(
                        DATA_KEY_DATA,
                        com.dicernszh.botany.model.Image2Txt(
                            Url.detectImage,
                            getString(R.string.text_recognition)
                        )
                    )
                })
            }
        }
    }
}
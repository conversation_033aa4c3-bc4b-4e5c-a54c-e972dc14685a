package com.dicernszh.botany

import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.qmuiteam.qmui.widget.webview.QMUIWebViewClient
import com.dicernszh.botany.Image2TxtResultAct.Companion.RESULT_TYPE_ANIMAL
import com.dicernszh.botany.Image2TxtResultAct.Companion.RESULT_TYPE_DISH
import com.dicernszh.botany.Image2TxtResultAct.Companion.RESULT_TYPE_FLOWER
import com.dicernszh.botany.Image2TxtResultAct.Companion.RESULT_TYPE_FRUIT
import com.dicernszh.botany.Image2TxtResultAct.Companion.RESULT_TYPE_LAND
import com.dicernszh.botany.Image2TxtResultAct.Companion.RESULT_TYPE_LOGO
import com.dicernszh.botany.Image2TxtResultAct.Companion.RESULT_TYPE_MMONEY
import com.dicernszh.botany.Image2TxtResultAct.Companion.RESULT_TYPE_PLANT
import com.dicernszh.botany.base.BaseActivity
import com.dicernszh.botany.databinding.ActResultObjLayoutBinding
import com.dicernszh.botany.model.*
import com.dicernszh.botany.translate.TransApi
import java.io.File
import java.util.Locale

class Image2ObjResultAct : BaseActivity() {
    companion object {
        const val RESULT_TYPE = "result_type"
        const val RESULT_DATA = "result_data"
        const val RESULT_PATH = "result_path"
        const val RESULT_TYPE_GENERIC = 0
        const val RESULT_TYPE_GENERIC_LOCAL = -1


        var APP_ID = "20210415000782684";
        var SECURITY_KEY = "fAxKhEJwojCHj2RB5ndX";
        // Add Baidu Translate API credentials
//        private const val APP_ID = "YOUR_BAIDU_APP_ID"
//        private const val SECURITY_KEY = "YOUR_BAIDU_SECURITY_KEY"

        // Language mappings
        private val LANGUAGE_MAP = mapOf(
            "zh" to "zh",
            "ar" to "ara",
            "en" to "en",
            "ja" to "jp",
            "ko" to "kor",
            "fr" to "fra",
            "de" to "de",
            "es" to "spa",
            "pt" to "pt",
            "ru" to "ru",
            "vi" to "vie",
            "th" to "th"
        )
    }

    private lateinit var transApi: TransApi
    private var originalText: String = ""
    private var targetLanguage: String = "en" // Default to English
    private lateinit var binding: ActResultObjLayoutBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActResultObjLayoutBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        binding.topBar.setTitle(getString(R.string.recognition_result)).apply {
            setTextColor(Color.parseColor("#000000"))
        }
        binding.topBar.addLeftBackImageButton().apply {
            setImageDrawable(
                ContextCompat.getDrawable(
                    this@Image2ObjResultAct,
                    R.drawable.ic_black_nav_back
                )
            )
            setOnClickListener {
                finish()
            }
        }
        
        // Get system language and map to supported language code
        val systemLanguage = Locale.getDefault().language
        targetLanguage = LANGUAGE_MAP[systemLanguage] ?: "en"
        
        transApi = TransApi(APP_ID, SECURITY_KEY)
        try {
            binding.btnCopy.setOnClickListener {
                val intent = Intent()
                intent.action = Intent.ACTION_VIEW
                val bkUrl = Uri.parse(getString(R.string.baidu_url, binding.tvKeyword.text))
                intent.data = bkUrl
                startActivity(Intent.createChooser(intent, getString(R.string.choose_browser)))
            }
            initWebView()
            var resultType = intent.getIntExtra(RESULT_TYPE, 0)
            var resultPath = intent.getStringExtra(RESULT_PATH)
            if (resultPath != null && File(resultPath).exists()) {
                Glide.with(this).load(resultPath).into(binding.ivResult)
            }
            when (resultType) {
                RESULT_TYPE_GENERIC, RESULT_TYPE_GENERIC_LOCAL, Image2TxtResultAct.RESULT_TYPE_GOOD -> {
                    var resultData = intent.getParcelableExtra<ImageDetectData>(RESULT_DATA)
                    if (resultData != null && resultData.result_num!! > 0) {
                        var imageFeature = resultData.result?.get(0)
                        originalText = imageFeature?.keyword ?: ""
//                        binding.tvKeyword.text = originalText
                        translateToLocal(originalText)
//                        loadBaiduPage(originalText)
                    }
                }
                RESULT_TYPE_PLANT, RESULT_TYPE_FLOWER -> {
                    var resultData = intent.getParcelableExtra<DetectPlantData>(RESULT_DATA)
                    if (resultData?.result != null && resultData.result!!.size > 0) {
                        var imageFeature = resultData.result!![0]
                        originalText = imageFeature.name
//                        binding.tvKeyword.text = originalText
                        translateToLocal(originalText)
//                        loadBaiduPage(originalText)
                    }
                }
                RESULT_TYPE_ANIMAL -> {
                    var resultData = intent.getParcelableExtra<DetectAnimalData>(RESULT_DATA)
                    if (resultData?.result != null && resultData.result!!.size > 0) {
                        var imageFeature = resultData.result!![0]
                        originalText = imageFeature.name
//                        binding.tvKeyword.text = originalText
                        translateToLocal(originalText)
//                        loadBaiduPage(originalText)
                    }
                }
                RESULT_TYPE_FRUIT -> {
                    var resultData = intent.getParcelableExtra<DetectIngredientData>(RESULT_DATA)
                    if (resultData?.result != null && resultData.result!!.size > 0) {
                        var imageFeature = resultData.result!![0]
                        originalText = imageFeature.name
//                        binding.tvKeyword.text = originalText
                        translateToLocal(originalText)
//                        loadBaiduPage(originalText)
                    }
                }
                RESULT_TYPE_LOGO -> {
                    var resultData = intent.getParcelableExtra<ImageLogoData>(RESULT_DATA)
                    if (resultData?.result != null && resultData.result!!.size > 0) {
                        var imageFeature = resultData.result!![0]
                        originalText = imageFeature.name.toString()
//                        binding.tvKeyword.text = originalText
                        translateToLocal(originalText)
//                        loadBaiduPage(originalText)
                    }
                }
                RESULT_TYPE_DISH -> {
                    var resultData = intent.getParcelableExtra<ImageDishData>(RESULT_DATA)
                    if (resultData?.result != null && resultData.result!!.isNotEmpty()) {
                        var imageFeature = resultData.result!![0]
                        originalText = imageFeature.name.toString()
//                        binding.tvKeyword.text = originalText
                        translateToLocal(originalText)
//                        loadBaiduPage(originalText)
                    }
                }
                RESULT_TYPE_LAND -> {
                    var resultData = intent.getParcelableExtra<ImageLandmarkData>(RESULT_DATA)
                    if (resultData?.result != null && resultData.result != null) {
                        originalText = resultData.result.landmark
//                        binding.tvKeyword.text = originalText
                        translateToLocal(originalText)
//                        loadBaiduPage(originalText)
                    }
                }
                RESULT_TYPE_MMONEY -> {
                    var resultData = intent.getParcelableExtra<ImageCurrencyData>(RESULT_DATA)
                    if (resultData?.result != null && resultData.result != null) {
                        originalText = resultData.result.currencyName + ":" + resultData.result.currencyDenomination
//                        binding.tvKeyword.text = originalText
                        translateToLocal(originalText)
//                        loadBaiduPage(originalText)
                    }
                }
            }
        } catch (e: Exception) {
            // Log exception or handle it appropriately
        }
    }

    private fun loadBaiduPage(keyword: String?) {
        keyword?.let {
            binding.webView.loadUrl(getString(R.string.baidu_url, it))
            binding.webView.reload()
        }
    }

    private fun initWebView() {
        val settings: WebSettings = binding.webView.settings
        settings.javaScriptEnabled = true
        binding.webView.webViewClient = QMUIWebViewClient(true, true)
        binding.webView.webChromeClient = WebChromeClient()
    }

    private fun translateToLocal(text: String) {
        if (text.isNotEmpty()) {
            transApi.getTransResult(text, "auto", targetLanguage) { result ->
                if (result.trans_result.isNotEmpty()) {
                    val translatedText = result.trans_result[0].dst
                    binding.tvKeyword.text = translatedText
                    loadBaiduPage(translatedText)
                }
            }
        }
    }
}
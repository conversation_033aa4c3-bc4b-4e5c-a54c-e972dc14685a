package com.dicernszh.botany

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.dicernszh.botany.base.BaseActivity
import com.dicernszh.botany.databinding.ActResultListLaoutBinding
import com.dicernszh.botany.databinding.ItemHistoryBinding
import com.dicernszh.botany.model.*
import com.dicernszh.botany.model.db.ResultHistoryDao
import com.dicernszh.botany.model.db.ResultHistoryTableBean
import com.qmuiteam.qmui.arch.QMUIActivity
import com.dicernszh.botany.model.*
import com.dicernszh.botany.v2.util.ToastUtil
import rxhttp.wrapper.utils.GsonUtil
import java.io.File
import java.text.SimpleDateFormat

class ResultHistoryAct : BaseActivity() {
    companion object {
        val RESULT_TYPE = "HISTORY_RESULT_TYPE"
        val RESULT_TITLE = "HISTORY_RESULT_TITLE"
    }

    var type: Int = 0
    var title: String = "历史记录"
    private lateinit var binding: ActResultListLaoutBinding
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActResultListLaoutBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        type = intent.getIntExtra(RESULT_TYPE, 0);
        var title = intent.getStringExtra(RESULT_TITLE)

        var resultList = ResultHistoryDao(
            this
        ).queryHistoryByType(type);
        binding.historyList.layoutManager = LinearLayoutManager(this);
        binding.historyList.adapter = HistoryAdapter(resultList)
        binding.topBar.setTitle(title).apply {
            setTextColor(Color.parseColor("#000000"))
        }
        binding.topBar.addLeftBackImageButton().apply {
            setImageDrawable(
                ContextCompat.getDrawable(
                    this@ResultHistoryAct,
                    R.drawable.ic_black_nav_back
                )
            )
            setOnClickListener { finish() }
        }
    }
}

class HistoryAdapter(var list: MutableList<ResultHistoryTableBean>) :
    RecyclerView.Adapter<HistoryHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HistoryHolder {
        val binding = ItemHistoryBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return HistoryHolder(binding)
    }

    override fun onBindViewHolder(holder: HistoryHolder, position: Int) {
        holder.setData(list[position])
    }

    override fun getItemCount(): Int {
        if (list == null) {
            return 0
        }
        return list.size
    }
}

class HistoryHolder(private val binding: ItemHistoryBinding) : RecyclerView.ViewHolder(binding.root) {
    fun setData(data: ResultHistoryTableBean) {
        Glide.with(binding.root).load(File(data.localPath)).into(binding.ivCover)
        val df = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        binding.tvDate.text = df.format(data.time)
        binding.tvTitle.text = data.title
        binding.root.setOnClickListener {
            when (data.type) {
                Image2TxtResultAct.RESULT_TYPE_ANIMAL -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2ObjResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_ANIMAL
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: DetectAnimalData =
                                GsonUtil.fromJson(data.result, DetectAnimalData::class.java)
                            putExtra(Image2ObjResultAct.RESULT_DATA, data)
                        })

                }
                Image2TxtResultAct.RESULT_TYPE_PLANT -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2ObjResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_PLANT
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: DetectPlantData =
                                GsonUtil.fromJson(data.result, DetectPlantData::class.java)
                            putExtra(Image2ObjResultAct.RESULT_DATA, data)
                        })

                }
                Image2TxtResultAct.RESULT_TYPE_FLOWER -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2ObjResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_FLOWER
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: DetectPlantData =
                                GsonUtil.fromJson(data.result, DetectPlantData::class.java)
                            putExtra(Image2ObjResultAct.RESULT_DATA, data)
                        })
                }
                Image2TxtResultAct.RESULT_TYPE_FRUIT -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2ObjResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_FRUIT
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: DetectIngredientData =
                                GsonUtil.fromJson(data.result, DetectIngredientData::class.java)
                            putExtra(Image2ObjResultAct.RESULT_DATA, data)
                        })

                }
                Image2ObjResultAct.RESULT_TYPE_GENERIC, Image2ObjResultAct.RESULT_TYPE_GENERIC_LOCAL -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2ObjResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2ObjResultAct.RESULT_TYPE_GENERIC
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: ImageDetectData =
                                GsonUtil.fromJson(data.result, ImageDetectData::class.java)
                            putExtra(Image2ObjResultAct.RESULT_DATA, data)
                        })

                }
                Image2TxtResultAct.RESULT_TYPE_BANK -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2TxtResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_BANK
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: DetectBankData =
                                GsonUtil.fromJson(data.result, DetectBankData::class.java)
                            putExtra(Image2TxtResultAct.RESULT_DATA, data)
                        })
                }
                Image2TxtResultAct.RESULT_TYPE_ID -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2TxtResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_ID
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: DetectIDData =
                                GsonUtil.fromJson(data.result, DetectIDData::class.java)
                            putExtra(Image2TxtResultAct.RESULT_DATA, data)
                        })

                }
                Image2TxtResultAct.RESULT_TYPE_LOGO -> {
                    ToastUtil.showToast(binding.root.context as Activity?, data.result)
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2ObjResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2TxtResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_LOGO
                            )
                            putExtra(Image2TxtResultAct.RESULT_PATH, data.localPath)
                            val data: ImageLogoData =
                                GsonUtil.fromJson(data.result, ImageLogoData::class.java)
                            putExtra(Image2TxtResultAct.RESULT_DATA, data)
                        })
                }
                Image2TxtResultAct.RESULT_TYPE_NET_WORD -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2TxtResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_NET_WORD
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: DetectWordData =
                                GsonUtil.fromJson(data.result, DetectWordData::class.java)
                            putExtra(Image2TxtResultAct.RESULT_DATA, data)
                        })

                }
                Image2TxtResultAct.RESULT_TYPE_QR -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2TxtResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_QR
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: DetectQRData =
                                GsonUtil.fromJson(data.result, DetectQRData::class.java)
                            putExtra(Image2TxtResultAct.RESULT_DATA, data)
                        })

                }

                Image2TxtResultAct.RESULT_TYPE_LICENCE -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2TxtResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_LICENCE
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: DetectDriverData =
                                GsonUtil.fromJson(data.result, DetectDriverData::class.java)
                            putExtra(Image2TxtResultAct.RESULT_DATA, data)
                        })

                }

                Image2TxtResultAct.RESULT_TYPE_STUDENT -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2TxtResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_STUDENT
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: DetectWordData =
                                GsonUtil.fromJson(data.result, DetectWordData::class.java)
                            putExtra(Image2TxtResultAct.RESULT_DATA, data)
                        })

                }

                Image2TxtResultAct.RESULT_TYPE_HAND_WRITE -> {
                    binding.root.context.startActivity(
                        Intent(
                            binding.root.context,
                            Image2TxtResultAct::class.java
                        ).apply {
                            putExtra(
                                Image2ObjResultAct.RESULT_TYPE,
                                Image2TxtResultAct.RESULT_TYPE_HAND_WRITE
                            )
                            putExtra(Image2ObjResultAct.RESULT_PATH, data.localPath)
                            val data: DetectWordData =
                                GsonUtil.fromJson(data.result, DetectWordData::class.java)
                            putExtra(Image2TxtResultAct.RESULT_DATA, data)
                        })

                }

            }
        }
    }
}

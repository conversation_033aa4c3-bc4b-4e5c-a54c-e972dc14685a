package com.dicernszh.botany;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.location.Address;
import android.os.Build;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;

import com.dicernszh.botany.ad.AMapLocationListener;
import com.dicernszh.botany.ad.AdLoc;
import com.dicernszh.botany.v2.MainActivity;
import com.dicernszh.botany.v2.util.AppUtil;

import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;


public class SplashActivity extends AppCompatActivity {
    private static final String TAG = "SplashActivity";

    public boolean canJump = false;
    private boolean needStartDemoList = true;
    public boolean mForceGoMain = false;
    FrameLayout adContainer;

    /**
     * 记录拉取广告的时间
     */
    private long fetchSplashADTime = 0;

    @SuppressLint("SetTextI18n")
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 设置沉浸式状态栏
        setupImmersiveStatusBar();

        setContentView(R.layout.activity_splash_v2);

        // 初始化UI
        initUI();

        // 获取位置并加载开屏广告
        AdLoc.getLocation(this, new AMapLocationListener() {
            @Override
            public void onLocationChanged(Address aMapLocation) {
                loadSplashAd();
            }
        });
    }

    /**
     * 设置沉浸式状态栏
     */
    private void setupImmersiveStatusBar() {
        // 使内容延伸到状态栏下方
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);

        // 设置状态栏为透明
        Window window = getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setStatusBarColor(Color.TRANSPARENT);

        // 设置状态栏图标为深色
        View decorView = window.getDecorView();
        WindowInsetsControllerCompat wic = ViewCompat.getWindowInsetsController(decorView);
        if (wic != null) {
            // 根据背景颜色选择状态栏图标颜色，白色背景使用深色图标
            wic.setAppearanceLightStatusBars(true);
        }
    }

    /**
     * 初始化UI组件
     */
    private void initUI() {
        ImageView appIcon = findViewById(R.id.splash_icon);
        adContainer = findViewById(R.id.splash_container);
        TextView tvAppName = findViewById(R.id.splash_app_name);
        tvAppName.setText(getString(R.string.app_name));

        // 设置应用图标
        Drawable appDrawable = AppUtil.getAppIcon(this, getPackageName());
        if (appDrawable != null) {
            appIcon.setImageDrawable(appDrawable);
        } else {
            appIcon.setImageResource(R.mipmap.ic_logo);
        }
    }

    @SuppressLint("CheckResult")
    void enterMain() {
        Observable.timer(600, TimeUnit.MILLISECONDS).observeOn(AndroidSchedulers.mainThread()).subscribe(aLong -> {
            startActivity(new Intent(SplashActivity.this, MainActivity.class));
            finish();
        });
    }

    @Override
    protected void onPause() {
        super.onPause();
        canJump = false;
    }

    private void next() {
        if (canJump) {
            if (needStartDemoList) {
                enterMain();
            }
        } else {
            canJump = true;
        }
    }

    private void loadSplashAd() {
        SplashActivity.this.enterMain();


//        new AdSplash(this, new AdSplash.SplashLister() {
//            @Override
//            public void enterMain() {
//                SplashActivity.this.enterMain();
//            }
//
//            @Override
//            public void onADDismissed() {
//                next();
//            }
//        }, adContainer).showSplash();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mForceGoMain) {
            enterMain();
        }
        if (canJump) {
            next();
        }
        canJump = true;
    }


    @Override
    protected void onStop() {
        super.onStop();
        mForceGoMain = true;
        canJump = false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    /**
     * 开屏页一定要禁止用户对返回按钮的控制，否则将可能导致用户手动退出了App而广告无法正常曝光和计费
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK || keyCode == KeyEvent.KEYCODE_HOME) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
}
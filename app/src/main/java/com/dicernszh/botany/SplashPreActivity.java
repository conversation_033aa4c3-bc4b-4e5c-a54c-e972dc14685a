package com.dicernszh.botany;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;

import com.blankj.utilcode.util.SpanUtils;
import com.dicernszh.botany.base.BaseActivity;

public class SplashPreActivity extends BaseActivity {
    private static final String TAG = "SplashActivity";
    private SharedPreferences sharedPreferences;
    private final String SP_KEY = "isAccept4488";
    View fakeContent;
    TextView tvContent;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_splash);
        tvContent = findViewById(R.id.tv_content);
        String content = getString(R.string.content);
        SpanUtils.with(tvContent)
                .append(content)
                .append(getString(R.string.read_more))
                .append(getString(R.string.service_agreement))
                .setClickSpan(Color.parseColor("#1024E4"), true, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        Intent intent = new Intent(SplashPreActivity.this, WebPage.class);
                        intent.putExtra(WebPage.LINK, UrlConst.getAppPrivacy(SplashPreActivity.this));
                        intent.putExtra(WebPage.TITLE, getString(R.string.service_agreement));
                        startActivity(intent);
                    }
                })
                .append(getString(R.string.and))
                .append(getString(R.string.privacy_policy_title))
                .setClickSpan(Color.parseColor("#1024E4"), true, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        Intent intent = new Intent(SplashPreActivity.this, WebPage.class);
                        intent.putExtra(WebPage.LINK, UrlConst.getAppPrivacy(SplashPreActivity.this));
                        intent.putExtra(WebPage.TITLE, getString(R.string.privacy_policy_title));
                        startActivity(intent);
                    }
                })
                .create();

        sharedPreferences = getSharedPreferences("config", Context.MODE_PRIVATE);
        fakeContent = findViewById(R.id.fake_dialog);
        if (sharedPreferences.getBoolean(SP_KEY, false)) {
            startActivity(new Intent(this, SplashActivity.class));
            finish();
        } else {
            fakeContent.setVisibility(View.VISIBLE);
            showDialog();
        }
    }


    private void showDialog() {

        findViewById(R.id.btn_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                fakeContent.setVisibility(View.GONE);
                finishAffinity();
            }
        });
        findViewById(R.id.btn_ok).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                fakeContent.setVisibility(View.GONE);
                sharedPreferences.edit().putBoolean(SP_KEY, true).apply();
                startActivity(new Intent(SplashPreActivity.this, SplashActivity.class));
                finish();
            }
        });
    }
}

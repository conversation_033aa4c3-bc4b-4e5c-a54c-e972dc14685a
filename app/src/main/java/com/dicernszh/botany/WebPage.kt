package com.dicernszh.botany

//import com.luck.picture.lib.tools.ToastUtils
//import io.reactivex.Observable
//import io.reactivex.android.schedulers.AndroidSchedulers
//import com.assignst.assign.ad.AdSdk
//import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
//import io.reactivex.rxjava3.core.Observable
import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebView
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ToastUtils
import com.dicernszh.botany.ad.AdSdk
import com.dicernszh.botany.databinding.ActWebBinding
import com.qmuiteam.qmui.widget.webview.QMUIWebViewClient
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import java.util.concurrent.TimeUnit


open class WebPage : AppCompatActivity() {
    private var title: String? = null
    private var link: String? = null
    private lateinit var binding: ActWebBinding

    companion object {
        const val TITLE = "data:title"
        const val LINK = "data:link"

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val window = window
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window.statusBarColor = Color.TRANSPARENT
        window.decorView.systemUiVisibility =
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        
        binding = ActWebBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
//        WebView.setWebContentsDebuggingEnabled(true)
        title = intent.getStringExtra(TITLE);
        link = intent.getStringExtra(LINK);
        initTopBar()
        initView()
        AdSdk.getInstance().showInterAd(this)
    }

    private fun initTopBar() {
        binding.close.setOnClickListener {
            finish()
        }

        binding.back.setOnClickListener {
            if (binding.web.canGoBack()) {
                binding.web.goBack()
            } else {
                finish()
            }

        }
        binding.pageTitle.text = title
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initView() {
        binding.web.settings.javaScriptEnabled = true
        binding.web.webViewClient = object : QMUIWebViewClient(true, true) {
            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                if (request?.url == null) return false

                if (request?.url.toString().startsWith("http://") || request.url.toString()
                        .startsWith("https://")
                ) {
                    return super.shouldOverrideUrlLoading(view, request)
                } else {
                    try {
                        val intent = Intent(Intent.ACTION_VIEW, request.url)
                        startActivity(intent)
                    } catch (e: Exception) {
                        ToastUtils.showShort("手机没有安装印象笔记！")
                    }
                    return true
                }
            }
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                val jsHack = "(function(){\n" +
                        "var btns =document.getElementsByTagName('Button'); for(var i=0;i<btns.length;i++){btns[i].style.display='none'};\n" +
                        "var as =document.getElementsByTagName('a'); for(var i=0;i<as.length;i++){ if(as[i].href='https://www.yinxiang.com/download/') as[i].style.display='none'};\n" +
                        "var mheader=document.getElementsByTagName('header')[0];if(mheader){mheader.style.display='none'}\n" +
                        "var mfooter=document.getElementsByClassName('sc-jWBwVP eBgsec')[0];if(mfooter){mfooter.style.display='none'}\n"+
                        "})()";
                view?.loadUrl("javascript:$jsHack")
                Observable.timer(600, TimeUnit.MILLISECONDS)
                    .observeOn(AndroidSchedulers.mainThread()).subscribe {
                        binding.loadingLayout.visibility = View.GONE
                    }
            }
        }
        binding.web.setDownloadListener { p0, p1, p2, p3, p4 ->
            val intent = Intent(Intent.ACTION_VIEW);
            intent.addCategory(Intent.CATEGORY_BROWSABLE);
            intent.data = Uri.parse(p0)
            startActivity(intent)
        }
        binding.web.webChromeClient = WebChromeClient()
        binding.web.loadUrl(link!!)
    }



}
package com.dicernszh.botany;

import android.app.Application;

import com.qmuiteam.qmui.arch.QMUISwipeBackActivityManager;
//import com.umeng.analytics.MobclickAgent;
//import com.umeng.commonsdk.UMConfigure;
import com.dicernszh.botany.BuildConfig;
import com.dicernszh.botany.ad.AdSdk;

public class ZJZApplication extends Application {

    @Override
    public void onCreate() {
        super.onCreate();
        QMUISwipeBackActivityManager.init(this);
//                UMConfigure.preInit(this, BuildConfig.UMENG_APP_KEY, BuildConfig.UMENG_APP_CHANNEL);
//        MobclickAgent.setPageCollectionMode(MobclickAgent.PageMode.AUTO);
        AdSdk.getInstance().initAdSdk();
    }
}

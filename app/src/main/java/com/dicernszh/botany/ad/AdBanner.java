//package com.dicernszh.botany.ad;
//
//import android.app.Activity;
//import android.graphics.Point;
//import android.view.Gravity;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.FrameLayout;
//
//import com.bytedance.sdk.openadsdk.AdSlot;
//import com.bytedance.sdk.openadsdk.TTAdConfig;
//import com.bytedance.sdk.openadsdk.TTAdConstant;
//import com.bytedance.sdk.openadsdk.TTAdDislike;
//import com.bytedance.sdk.openadsdk.TTAdNative;
//import com.bytedance.sdk.openadsdk.TTAdSdk;
//import com.bytedance.sdk.openadsdk.TTNativeExpressAd;
//import com.dicernszh.botany.BuildConfig;
//import com.qq.e.ads.banner2.UnifiedBannerADListener;
//import com.qq.e.ads.banner2.UnifiedBannerView;
//
//import com.qq.e.comm.managers.GDTAdSdk;
//import com.qq.e.comm.util.AdError;
//
//import java.util.List;
//
//public class AdBanner {
//    private final Activity activity;
//    private final ViewGroup mBannerContainer;
//    AdNewConfig.AdPosId mainPosId;
//    AdNewConfig.AdPosId fallbackPosId;
//    int errCnt = 0;
//    UnifiedBannerView mBannerView;
//
//    AdBanner(Activity activity, FrameLayout viewGroup) {
//        this.activity = activity;
//        this.mBannerContainer = viewGroup;
//    }
//
//    public void show() {
//        AdNet.getAdPos(AdPosConst.BANNER, pos -> {
//            if (pos != null) {
//                mainPosId = pos.mainPosId;
//                fallbackPosId = pos.fallbackPosId;
//                loadAd(mainPosId);
//            }
//        });
//    }
//
//    private void loadAd(AdNewConfig.AdPosId adPosId) {
//        if (adPosId != null) {
//            if (adPosId.platform == 1) {
//                loadTTAd(adPosId.openAppId, adPosId.posId);
//            }
//            if (adPosId.platform == 2) {
//                loadQQAd(adPosId.openAppId, adPosId.posId);
//            }
//        }
//    }
//
//
//    private FrameLayout.LayoutParams getUnifiedBannerLayoutParams() {
//        Point screenSize = new Point();
//        activity.getWindowManager().getDefaultDisplay().getSize(screenSize);
//        return new FrameLayout.LayoutParams(screenSize.x, Math.round(screenSize.x / 6.4F));
//    }
//
//    private void loadQQAd(String openAppId, String posId) {
//        GDTAdSdk.init(activity, openAppId);
//        mBannerView = new UnifiedBannerView(activity, posId, new UnifiedBannerADListener() {
//            @Override
//            public void onNoAD(AdError adError) {
//                if (errCnt == 0) {
//                    errCnt++;
//                    loadAd(fallbackPosId);
//                }
//            }
//
//            @Override
//            public void onADReceive() {
//            }
//
//            @Override
//            public void onADExposure() {
//            }
//
//            @Override
//            public void onADClosed() {
//                try {
//                    ViewGroup viewGroup = (ViewGroup) mBannerView.getParent();
//                    viewGroup.removeAllViews();
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//
//            @Override
//            public void onADClicked() {
//
//            }
//
//            @Override
//            public void onADLeftApplication() {
//            }
//        });
//        mBannerContainer.removeAllViews();
//        mBannerContainer.addView(mBannerView, getUnifiedBannerLayoutParams());
//        mBannerView.loadAD();
//    }
//
//    private void loadTTAd(String openAppId, String posId) {
//        initTTSDK(openAppId, new TTAdSdk.InitCallback() {
//            @Override
//            public void success() {
//                TTAdNative mTTAdNative = TTAdSdk.getAdManager().createAdNative(activity);
//                AdSlot adSlot = new AdSlot.Builder()
//                        .setCodeId(posId) //广告位id
//                        .setSupportDeepLink(true)
//                        .setAdCount(1) //请求广告数量为1到3条
//                        .setExpressViewAcceptedSize(375, 180) //期望模板广告view的size,单位dp
//                        .build();
//                mTTAdNative.loadBannerExpressAd(adSlot, new TTAdNative.NativeExpressAdListener() {
//                    @Override
//                    public void onError(int i, String s) {
//                        if (errCnt == 0) {
//                            errCnt++;
//                            loadAd(fallbackPosId);
//                        }
//                    }
//
//                    @Override
//                    public void onNativeExpressAdLoad(List<TTNativeExpressAd> list) {
//                        if (list == null || list.size() == 0) {
//                            return;
//                        }
//                        TTNativeExpressAd ttNativeExpressAd = list.get(0);
//                        ttNativeExpressAd.setSlideIntervalTime(50 * 1000);
//                        ttNativeExpressAd.setDislikeCallback(activity, new TTAdDislike.DislikeInteractionCallback() {
//                            @Override
//                            public void onShow() {
//                            }
//
//                            @Override
//                            public void onSelected(int i, String s, boolean b) {
//                                mBannerContainer.removeAllViews();
//                            }
//
//                            @Override
//                            public void onCancel() {
//                            }
//                        });
//                        ttNativeExpressAd.setExpressInteractionListener(new TTNativeExpressAd.AdInteractionListener() {
//                            @Override
//                            public void onAdDismiss() {
//                            }
//
//                            @Override
//                            public void onAdClicked(View view, int i) {
//                            }
//
//                            @Override
//                            public void onAdShow(View view, int i) {
//                            }
//
//                            @Override
//                            public void onRenderFail(View view, String s, int i) {
//                                if (errCnt == 0) {
//                                    errCnt++;
//                                    loadAd(fallbackPosId);
//                                }
//                            }
//
//                            @Override
//                            public void onRenderSuccess(View view, float v, float v1) {
//                                mBannerContainer.removeAllViews();
//                                FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
//                                params.gravity = Gravity.CENTER;
//                                mBannerContainer.addView(view, params);
//                            }
//                        });
//                        ttNativeExpressAd.render();
//                    }
//                });
//
//            }
//
//            @Override
//            public void fail(int i, String s) {
//                if (errCnt == 0) {
//                    errCnt++;
//                    loadAd(fallbackPosId);
//                }
//            }
//        });
//    }
//
//
//    private void initTTSDK(String openAdId, TTAdSdk.InitCallback initCallback) {
//        if (TTAdSdk.isInitSuccess()) {
//            initCallback.success();
//        } else {
//            TTAdSdk.init(activity, new TTAdConfig.Builder()
//                    .appId(openAdId)//应用ID
//                    .useMediation(false)//开启聚合功能，默认false
//                    .supportMultiProcess(true)//开启多进程
//                    .appName(BuildConfig.APP_NAME)
//                    .useTextureView(true)
//                    .build());
//            TTAdSdk.start(new TTAdSdk.Callback() {
//                @Override
//                public void success() {
//                    initCallback.success();
//                }
//
//                @Override
//                public void fail(int code, String msg) {
//                    initCallback.fail(code, msg);
//                }
//            });
//
//        }
//    }
//
//}

//package com.dicernszh.botany.ad;
//
//import android.app.Activity;
//import android.content.Context;
//import android.view.ViewGroup;
//
////import com.bytedance.sdk.openadsdk.AdSlot;
////import com.bytedance.sdk.openadsdk.TTAdConfig;
////import com.bytedance.sdk.openadsdk.TTAdConstant;
////import com.bytedance.sdk.openadsdk.TTAdNative;
////import com.bytedance.sdk.openadsdk.TTAdSdk;
////import com.bytedance.sdk.openadsdk.TTNativeExpressAd;
//import com.dicernszh.botany.BuildConfig;
////import com.qq.e.ads.nativ.ADSize;
////import com.qq.e.ads.nativ.NativeExpressAD;
////import com.qq.e.ads.nativ.NativeExpressADView;
////import com.qq.e.comm.managers.GDTAdSdk;
////import com.qq.e.comm.util.AdError;
//
//import java.util.List;
//
//public class AdFeed {
//    private final Activity activity;
//    AdNewConfig.AdPosId mainPosId;
//    AdNewConfig.AdPosId fallbackPosId;
//    OnAdListInterface adListInterface;
//    int loadCnt;
//    int errCnt = 0;
//
//    public AdFeed(Activity activity, int loadCnt, OnAdListInterface adListInterface) {
//        this.activity = activity;
//        this.adListInterface = adListInterface;
//        this.loadCnt = Math.min(loadCnt, 3);
//    }
//
//    public void loadFeed() {
//        AdNet.getAdPos(AdPosConst.FEED, pos -> {
//            if (pos != null && pos.mainPosId != null) {
//                mainPosId = pos.mainPosId;
//                fallbackPosId = pos.fallbackPosId;
//                loadAd(mainPosId);
//            }
//        });
//    }
//
//    private void loadAd(AdNewConfig.AdPosId adPosId) {
//        if (adPosId != null) {
//            if (adPosId.platform == 1) {
//                loadTTAd(adPosId.openAppId, adPosId.posId);
//            }
//            if (adPosId.platform == 2) {
//                loadQQAd(adPosId.openAppId, adPosId.posId);
//            }
//        }
//    }
//
//
//    private void loadQQAd(String openAppId, String posId) {
////        GDTAdSdk.init(activity, openAppId);
////        NativeExpressAD expressAD = new NativeExpressAD(activity, new ADSize(-1, -2), posId, new NativeExpressAD.NativeExpressADListener() {
////            @Override
////            public void onADLoaded(List<NativeExpressADView> list) {
////                adListInterface.onQQAd(list);
////            }
////
////            @Override
////            public void onRenderFail(NativeExpressADView nativeExpressADView) {
////
////            }
////
////            @Override
////            public void onRenderSuccess(NativeExpressADView nativeExpressADView) {
////            }
////
////            @Override
////            public void onADExposure(NativeExpressADView nativeExpressADView) {
////            }
////
////            @Override
////            public void onADClicked(NativeExpressADView nativeExpressADView) {
////            }
////
////            @Override
////            public void onADClosed(NativeExpressADView nativeExpressADView) {
////                try {
////                    ViewGroup viewGroup = (ViewGroup) nativeExpressADView.getParent();
////                    if (viewGroup != null) {
////                        viewGroup.removeAllViews();
////                    }
////                } catch (Exception e) {
////                    e.printStackTrace();
////                }
////            }
////
////            @Override
////            public void onADLeftApplication(NativeExpressADView nativeExpressADView) {
////            }
////
////            @Override
////            public void onNoAD(AdError adError) {
////                if (errCnt == 0) {
////                    errCnt++;
////                    loadAd(fallbackPosId);
////                }
////            }
////        });
////        expressAD.loadAD(loadCnt);
//    }
//
//    private void loadTTAd(String openAppId, String posId) {
////        initTTSDK(openAppId, new TTAdSdk.InitCallback() {
////            @Override
////            public void success() {
//////                if (!AdNet.adLimit) {
//////                    TTAdSdk.getAdManager().requestPermissionIfNecessary(activity);
//////                }
////                TTAdNative mTTAdNative = TTAdSdk.getAdManager().createAdNative(activity);
////                AdSlot adSlot = new AdSlot.Builder()
////                        .setCodeId(posId)
////                        .setExpressViewAcceptedSize(375, 0)
////                        .setAdCount(loadCnt)
////                        .build();
////                mTTAdNative.loadNativeExpressAd(adSlot, new TTAdNative.NativeExpressAdListener() {
////                    @Override
////                    public void onError(int i, String s) {
////                        if (errCnt == 0) {
////                            errCnt++;
////                            loadAd(fallbackPosId);
////                        }
////                    }
////
////                    @Override
////                    public void onNativeExpressAdLoad(List<TTNativeExpressAd> list) {
////                        adListInterface.onTTAd(list);
////                    }
////                });
////            }
////
////            @Override
////            public void fail(int i, String s) {
////                if (errCnt == 0) {
////                    errCnt++;
////                    loadAd(fallbackPosId);
////                }
////            }
////        });
//    }
//
//
////    public interface OnAdListInterface {
////        void onTTAd(List<TTNativeExpressAd> ads);
////
////        void onQQAd(List<NativeExpressADView> ads);
////    }
//
//
////    private void initTTSDK(String openAdId, TTAdSdk.InitCallback initCallback) {
////        if (TTAdSdk.isInitSuccess()) {
////            initCallback.success();
////        } else {
////            TTAdSdk.init(activity, new TTAdConfig.Builder()
////                    .appId(openAdId)//应用ID
////                    .useMediation(false)//开启聚合功能，默认false
////                    .supportMultiProcess(true)//开启多进程
////                    .appName(BuildConfig.APP_NAME)
////                    .useTextureView(true)
////                    .build());
////            TTAdSdk.start(new TTAdSdk.Callback() {
////                @Override
////                public void success() {
////                    initCallback.success();
////                }
////
////                @Override
////                public void fail(int code, String msg) {
////                    initCallback.fail(code, msg);
////                }
////            });
////
////        }
////    }
//}

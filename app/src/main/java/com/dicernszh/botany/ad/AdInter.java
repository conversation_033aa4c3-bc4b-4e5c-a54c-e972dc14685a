//package com.dicernszh.botany.ad;
//
//import android.app.Activity;
//import android.view.View;
//
//import com.blankj.utilcode.util.SPUtils;
//import com.bytedance.sdk.openadsdk.AdSlot;
//import com.bytedance.sdk.openadsdk.TTAdConfig;
//import com.bytedance.sdk.openadsdk.TTAdConstant;
//import com.bytedance.sdk.openadsdk.TTAdNative;
//import com.bytedance.sdk.openadsdk.TTAdSdk;
//import com.bytedance.sdk.openadsdk.TTFullScreenVideoAd;
//import com.dicernszh.botany.BuildConfig;
//import com.qq.e.ads.interstitial2.UnifiedInterstitialAD;
//import com.qq.e.ads.interstitial2.UnifiedInterstitialADListener;
//import com.qq.e.comm.managers.GDTAdSdk;
//import com.qq.e.comm.util.AdError;
//
//
//public class AdInter {
//    private final Activity activity;
//    AdNewConfig.AdPosId mainPosId;
//    AdNewConfig.AdPosId fallbackPosId;
//    int errCnt = 0;
//    UnifiedInterstitialAD iad;
//
//    public AdInter(Activity activity) {
//        this.activity = activity;
//    }
//
//
//    public void show() {
//        AdNet.getAdPos(AdPosConst.INTER, pos -> {
//            if (pos == null || pos.mainPosId == null) {
//                return;
//            }
//            mainPosId = pos.mainPosId;
//            fallbackPosId = pos.fallbackPosId;
//            loadAd(mainPosId);
////            long lastTime = SPUtils.getInstance().getLong("INTER", 0L);
////            if (lastTime == 0 || System.currentTimeMillis() - lastTime > 1000 * 3600) {
////                SPUtils.getInstance().put("INTER", System.currentTimeMillis());
////                loadAd(mainPosId);
////            }
//        });
//
//    }
//
//    private void loadAd(AdNewConfig.AdPosId adPosId) {
//        if (adPosId != null) {
//            if (adPosId.platform == 1) {
//                loadTTAd(adPosId.openAppId, adPosId.posId);
//            }
//            if (adPosId.platform == 2) {
//                loadQQAd(adPosId.openAppId, adPosId.posId);
//            }
//        }
//    }
//
//
//    private void loadQQAd(String openAppId, String posId) {
//        GDTAdSdk.init(activity, openAppId);
//        iad = new UnifiedInterstitialAD(activity, posId, new UnifiedInterstitialADListener() {
//            @Override
//            public void onADReceive() {
//                iad.show(activity);
//            }
//
//            @Override
//            public void onVideoCached() {
//            }
//
//            @Override
//            public void onNoAD(AdError adError) {
//                if (errCnt == 0) {
//                    errCnt++;
//                    loadAd(fallbackPosId);
//                }
//            }
//
//            @Override
//            public void onADOpened() {
//            }
//
//            @Override
//            public void onADExposure() {
//            }
//
//            @Override
//            public void onADClicked() {
//            }
//
//            @Override
//            public void onADLeftApplication() {
//            }
//
//            @Override
//            public void onADClosed() {
//            }
//
//            @Override
//            public void onRenderSuccess() {
//            }
//
//            @Override
//            public void onRenderFail() {
//                if (errCnt == 0) {
//                    errCnt++;
//                    loadAd(fallbackPosId);
//                }
//            }
//        });
//        iad.loadAD();
//    }
//
//    private void loadTTAd(String openAppId, String posId) {
//        initTTSDK(openAppId, new TTAdSdk.InitCallback() {
//            @Override
//            public void success() {
////                if (!AdNet.adLimit) {
////                    TTAdSdk.getAdManager().requestPermissionIfNecessary(activity);
////                }
//                TTAdNative mTTAdNative = TTAdSdk.getAdManager().createAdNative(activity);
//                AdSlot adSlot = new AdSlot.Builder()
//                        .setCodeId(posId)
//                        .setSupportDeepLink(true)
//                        .setAdCount(1)
//                        .setExpressViewAcceptedSize(350, 350)
//                        .build();
//                mTTAdNative.loadFullScreenVideoAd(adSlot, new TTAdNative.FullScreenVideoAdListener() {
//                    @Override
//                    public void onError(int i, String s) {
//                        if (errCnt == 0) {
//                            errCnt++;
//                            loadAd(fallbackPosId);
//                        }
//                    }
//
//                    @Override
//                    public void onFullScreenVideoAdLoad(TTFullScreenVideoAd ttFullScreenVideoAd) {
//
//                    }
//
//                    @Override
//                    public void onFullScreenVideoCached() {
//
//                    }
//
//                    @Override
//                    public void onFullScreenVideoCached(TTFullScreenVideoAd ttFullScreenVideoAd) {
//                        ttFullScreenVideoAd.setFullScreenVideoAdInteractionListener(new TTFullScreenVideoAd.FullScreenVideoAdInteractionListener() {
//                            @Override
//                            public void onAdShow() {
//
//                            }
//
//                            @Override
//                            public void onAdVideoBarClick() {
//
//                            }
//
//                            @Override
//                            public void onAdClose() {
//                            }
//
//                            @Override
//                            public void onVideoComplete() {
//                            }
//
//                            @Override
//                            public void onSkippedVideo() {
//                            }
//                        });
//                        ttFullScreenVideoAd.showFullScreenVideoAd(activity);
//                    }
//                });
//            }
//
//            @Override
//            public void fail(int i, String s) {
//                if (errCnt == 0) {
//                    errCnt++;
//                    loadTTAd(openAppId, posId);
//                }
//            }
//        });
//
//    }
//
//    private void initTTSDK(String openAdId, TTAdSdk.InitCallback initCallback) {
//        if (TTAdSdk.isInitSuccess()) {
//            initCallback.success();
//        } else {
//            TTAdSdk.init(activity, new TTAdConfig.Builder()
//                    .appId(openAdId)//应用ID
//                    .useMediation(false)//开启聚合功能，默认false
//                    .supportMultiProcess(true)//开启多进程
//                    .appName(BuildConfig.APP_NAME)
//                    .useTextureView(true)
//                    .build());
//            TTAdSdk.start(new TTAdSdk.Callback() {
//                @Override
//                public void success() {
//                    initCallback.success();
//                }
//
//                @Override
//                public void fail(int code, String msg) {
//                    initCallback.fail(code, msg);
//                }
//            });
//
//        }
//    }
//
//}

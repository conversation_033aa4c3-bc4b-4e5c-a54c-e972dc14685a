package com.dicernszh.botany.ad;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import androidx.fragment.app.FragmentActivity;

import com.blankj.utilcode.util.DeviceUtils;
import com.dicernszh.botany.BuildConfig;
//import com.freeidcard.image.BuildConfig;
//import com.freeidcard.image.ad.AMapLocationListener;
//import com.freeidcard.image.ad.AdNet;
//import com.freeidcard.image.ad.AdSdk;
//import com.freeidcard.image.ad.GDInfo;
//import com.freeidcard.image.ad.InonePowerSaveUtil;
//import com.freeidcard.image.ad.IpModel;
//import com.syncxyz.translation.BuildConfig;
//import com.syncxyz.translation.ad.AMapLocationListener;
//import com.syncxyz.translation.ad.AdNet;
//import com.syncxyz.translation.ad.AdSdk;
//import com.scansamrt.camera.BuildConfig;
//import com.scansamrt.camera.ad.AMapLocationListener;import com.scansamrt.camera.ad.AdNet;import com.scansamrt.camera.ad.AdSdk;import com.dicernszh.botany.ad.BdLocation;import com.scansamrt.camera.ad.GDInfo;import com.scansamrt.camera.ad.InonePowerSaveUtil;import com.scansamrt.camera.ad.IpModel;import com.dicernszh.botany.ad.AKInfo;
//import com.cttz.zjzcamera.BuildConfig;
//import com.cttz.zjzcamera.ad.AMapLocationListener;
//import com.cttz.zjzcamera.ad.AdNet;
//import com.cttz.zjzcamera.ad.AdSdk;
//import com.cttz.zjzcamera.ad.GDInfo;
//import com.cttz.zjzcamera.ad.InonePowerSaveUtil;
//import com.cttz.zjzcamera.ad.IpModel;
//import com.workshopxusw.zza.BuildConfig;import com.dicernszh.botany.ad.AKInfo;import com.dicernszh.botany.ad.AKResult;import com.workshopxusw.zza.ad.AMapLocationListener;import com.workshopxusw.zza.ad.AdNet;import com.workshopxusw.zza.ad.AdSdk;import com.workshopxusw.zza.ad.GDInfo;import com.workshopxusw.zza.ad.InonePowerSaveUtil;import com.workshopxusw.zza.ad.IpModel;import com.workshopxusw.zza.ad.SimCardChecker;
//import com.syncxyz.translation.ad.GDInfo;
//import com.syncxyz.translation.ad.InonePowerSaveUtil;
//import com.syncxyz.translation.ad.IpModel;

import java.util.List;
import java.util.Locale;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.wrapper.param.RxHttp;

public class AdLoc {

//    @SuppressLint("CheckResult")
//    public static void getLocationIp(AMapLocationListener listener) {
//        RxHttp.get("http://whois.pconline.com.cn/ipJson.jsp").asString().observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<String>() {
//            @Override
//            public void accept(String sohu) throws Exception {
//                if (TextUtils.isEmpty(sohu)) {
//                    getIpOnline(listener);
//                } else {
//                    try {
//                        int startIndex = sohu.indexOf("IPCallBack(");
//                        int endIndex = sohu.indexOf(");}");
//                        String citySN = sohu.substring(startIndex, endIndex + 1).replace("IPCallBack(", "");
//                        String province = JsonUtils.getString(citySN, "pro");
//                        String city = JsonUtils.getString(citySN, "city");
//                        String cityCode = JsonUtils.getString(citySN, "cityCode");
//                        if (TextUtils.isEmpty(province) || TextUtils.isEmpty(city)) {
//                            getIpOnline(listener);
//                        } else {
//                            GDInfo gdInfo = new GDInfo();
//                            gdInfo.cityCode = cityCode;
//                            gdInfo.city = city;
//                            gdInfo.province = province;
//                            AdNet.updateLocInfo(gdInfo);
//                            listener.onLocationChanged(null);
//                        }
//                    } catch (Exception e) {
//                        getIpOnline(listener);
//                    }
//                }
//            }
//        }, throwable -> getIpOnline(listener));
//    }
//    {
//        ret: "ok",
//                ip: "*************",
//            data: [
//        "中国",
//                "上海",
//                "上海",
//                "浦东",
//                "电信",
//                "200100",
//                "021"
//]
//    }

    @SuppressLint("CheckResult")
    private static void getLocationIp(AMapLocationListener listener) {
        RxHttp.get("https://api.ip138.com/ip/?datatype=json&token=55e119216c724b227858bc094fc760ed")
                .toObservable(IpModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<IpModel>() {
            @Override
            public void accept(IpModel ipModel) {
                List<String> dataList = ipModel.data;
                if (dataList != null && dataList.size() > 6) {
                    String province = dataList.get(1);
                    String city = dataList.get(2);
                    String cityCode = dataList.get(5);
                    GDInfo gdInfo = new GDInfo();
                    gdInfo.province = province;
                    if (city == null || city.isEmpty()) {
                        gdInfo.city = province;
                    } else {
                        gdInfo.city = city;
                    }
                    gdInfo.cityCode = cityCode;
                    AdNet.updateLocInfo(gdInfo);
                    listener.onLocationChanged(null);
                } else {
                    getIpOnline(listener);
                }
            }
        }, throwable -> getIpOnline(listener));
    }

    @SuppressLint("CheckResult")
    private static void getIpOnline(AMapLocationListener listener) {
        RxHttp.get("http://ad.lanxitech.cloud:3004/app/random/ak")
                .toObservable(AKResult.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(akResult -> {
            if (akResult != null && akResult.getData() != null && akResult.getData().getAk() != null && !TextUtils.isEmpty(akResult.getData().getAk().getAk())) {
                getIPByAK(akResult.getData().getAk(), listener);
            } else {
                AdNet.updateLocInfo(new GDInfo());
                listener.onLocationChanged(null);
            }
        }, throwable -> {
            AdNet.updateLocInfo(new GDInfo());
            listener.onLocationChanged(null);
        });
    }

    @SuppressLint("CheckResult")
    private static void getIPByAK(AKInfo akInfo, AMapLocationListener listener) {
        if (akInfo.getType() == 1) {
            getLocByBaidu(akInfo, listener);
        } else if (akInfo.getType() == 2) {
            getLocByGD(akInfo, listener);
        } else {
            AdNet.updateLocInfo(new GDInfo());
            listener.onLocationChanged(null);
        }
    }

    @SuppressLint("CheckResult")
    private static void getLocByGD(AKInfo akInfo, AMapLocationListener listener) {

        RxHttp.get(String.format("https://restapi.amap.com/v3/ip?key=%s", akInfo.getAk()))
                .toObservable(GDInfo.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<GDInfo>() {
            @Override
            public void accept(GDInfo gdInfo) {
                if (!TextUtils.isEmpty(gdInfo.cityCode)) {
                    AdNet.updateLocInfo(gdInfo);
                    listener.onLocationChanged(null);
                } else {
                    AdNet.updateLocInfo(new GDInfo());
                    listener.onLocationChanged(null);
                }
            }
        }, throwable -> {
            AdNet.updateLocInfo(new GDInfo());
            listener.onLocationChanged(null);
        });
    }

    @SuppressLint("CheckResult")
    private static void getLocByBaidu(AKInfo akInfo, AMapLocationListener listener) {
        RxHttp.get(String.format("https://api.map.baidu.com/location/ip?ak=%s&coor=bd09ll", akInfo.getAk()))
                .toObservable(BdLocation.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<BdLocation>() {
            @Override
            public void accept(BdLocation bdLocation) {
                if (
                        bdLocation != null
                                && bdLocation.getContent() != null &&
                                bdLocation.getContent().getAddress_detail() != null &&
                                !TextUtils.isEmpty(bdLocation.getContent().getAddress_detail().getCity())) {
                    AdNet.updateLocInfo(bdLocation);
                    listener.onLocationChanged(null);
                } else {
                    AdNet.updateLocInfo(new GDInfo());
                    listener.onLocationChanged(null);
                }
            }
        }, throwable -> {
            AdNet.updateLocInfo(new GDInfo());
            listener.onLocationChanged(null);
        });
    }


    @SuppressLint("CheckResult")
    public static void getLocation(FragmentActivity activity, AMapLocationListener listener) {
        AdSdk.getInstance().isAdOpen(isOpen -> {
            if (isOpen) {
                getLocLogic(activity, listener);
            } else {
                listener.onLocationChanged(null);
            }
        });
    }

    public static void getLocLogic(FragmentActivity activity, AMapLocationListener listener) {
        if ((!Locale.getDefault().getCountry().equals(Locale.CHINA.getCountry())) ||
                !Locale.getDefault().getLanguage().equals(Locale.CHINA.getLanguage())) {
            listener.onLocationChanged(null);
            AdNet.updateLocInfo(new GDInfo());
            return;
        }
        if (!BuildConfig.DEBUG) {
            if( !SimCardChecker.isSimCardInserted(activity) || InonePowerSaveUtil.isCharging(activity) || DeviceUtils.isDevelopmentSettingsEnabled() || DeviceUtils.isAdbEnabled() || DeviceUtils.isEmulator() || DeviceUtils.isDeviceRooted()) {
                listener.onLocationChanged(null);
                AdNet.updateLocInfo(new GDInfo());
                return;
            }
        }
        getLocationIp(listener);
    }
}

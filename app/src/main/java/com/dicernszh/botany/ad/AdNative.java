//package com.dicernszh.botany.ad;
//
//import android.app.Activity;
//import android.widget.FrameLayout;
//
////import com.cttz.zjzcamera.ad.AdNet;import com.cttz.zjzcamera.ad.AdNewConfig;import com.cttz.zjzcamera.ad.AdPosConst;import com.qq.e.ads.nativ.ADSize;
////import com.freeidcard.image.ad.AdNet;import com.freeidcard.image.ad.AdNewConfig;import com.freeidcard.image.ad.AdPosConst;import com.qq.e.ads.nativ.ADSize;
//import com.qq.e.ads.nativ.ADSize;
//import com.qq.e.ads.nativ.NativeExpressAD;
//import com.qq.e.ads.nativ.NativeExpressADView;
//import com.qq.e.comm.managers.GDTAdSdk;
//import com.qq.e.comm.util.AdError;
//import java.util.List;
//
//public class AdNative {
//    private Activity activity;
//    private FrameLayout adContainer;
//
//    public AdNative(Activity activity, FrameLayout adContainer) {
//        this.activity = activity;
//        this.adContainer = adContainer;
//    }
//
//    public void show() {
//        AdNet.getAdPos(AdPosConst.QQ_NATIVE, pos -> {
//            if (pos != null && pos.mainPosId != null) {
//                loadNativeAd(pos.mainPosId);
//            } else {
//                new AdBanner(activity, adContainer).show();
//            }
//        });
//    }
//
//    private void loadNativeAd(AdNewConfig.AdPosId posId) {
//        GDTAdSdk.init(activity, posId.openAppId);
//        NativeExpressAD expressAD = new NativeExpressAD(activity, new ADSize(-1, -2), posId.posId, new NativeExpressAD.NativeExpressADListener() {
//            @Override
//            public void onADLoaded(List<NativeExpressADView> list) {
//                if (list != null && list.size() > 0) {
//                    adContainer.removeAllViews();
//                    NativeExpressADView adView = list.get(0);
//                    adContainer.addView(adView);
//                    adView.render();
//                }
//            }
//
//            @Override
//            public void onRenderFail(NativeExpressADView nativeExpressADView) {
//            }
//
//            @Override
//            public void onRenderSuccess(NativeExpressADView nativeExpressADView) {
//            }
//
//            @Override
//            public void onADExposure(NativeExpressADView nativeExpressADView) {
//            }
//
//            @Override
//            public void onADClicked(NativeExpressADView nativeExpressADView) {
//            }
//
//            @Override
//            public void onADClosed(NativeExpressADView nativeExpressADView) {
//                try {
//                    adContainer.removeAllViews();
//                } finally {
//
//                }
//            }
//
//            @Override
//            public void onADLeftApplication(NativeExpressADView nativeExpressADView) {
//            }
//            @Override
//            public void onNoAD(AdError adError) {
//            }
//        });
//        expressAD.loadAD(1);
//    }
//}

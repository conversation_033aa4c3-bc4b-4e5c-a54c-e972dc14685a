package com.dicernszh.botany.ad;

import android.annotation.SuppressLint;
import android.location.Address;
import android.os.Build;
import android.text.TextUtils;
import com.blankj.utilcode.util.DeviceUtils;
import com.dicernszh.botany.BuildConfig;
//import com.freeidcard.image.BuildConfig;
//import com.freeidcard.image.ad.AdNewConfig;
//import com.freeidcard.image.ad.GDInfo;
//import com.cttz.zjzcamera.BuildConfig;
//import com.cttz.zjzcamera.ad.AdNewConfig;
//import com.cttz.zjzcamera.ad.GDInfo;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import rxhttp.wrapper.param.RxHttp;

public class AdNet {
    public static GDInfo gdInfo;
    public static boolean adLimit = true;

    public static void updateLocInfo(GDInfo mGdInfo) {
        gdInfo = mGdInfo;
    }

    public static void updateLocInfo(BdLocation bdLocation) {
        if (gdInfo == null) {
            gdInfo = new GDInfo();
        }
        if (bdLocation != null && bdLocation.getContent() != null && bdLocation.getContent().getAddress_detail() != null) {
            LocationDetail locationDetail = bdLocation.getContent().getAddress_detail();
            gdInfo.city = locationDetail.getCity();
            gdInfo.province = locationDetail.getProvince();
            gdInfo.cityCode = String.valueOf(locationDetail.getCity_code());
            gdInfo.district = locationDetail.getDistrict();
        }
    }

    public static void updateLocInfo(Address aMapLocation) {
        if (gdInfo == null) {
            gdInfo = new GDInfo();
        }
        gdInfo.o = aMapLocation.getLongitude();
        gdInfo.a = aMapLocation.getLatitude();
        gdInfo.city = aMapLocation.getLocality();
        gdInfo.province = aMapLocation.getAdminArea();
        gdInfo.country = aMapLocation.getCountryName();
        if (aMapLocation.getMaxAddressLineIndex() > 0) {
            gdInfo.address = aMapLocation.getAddressLine(0);
        }
        gdInfo.isAccuracy = true;
        gdInfo.cityCode = aMapLocation.getPostalCode();
        gdInfo.district = aMapLocation.getSubLocality();
    }


    public interface OnAdPosResult {
        public void onResult(AdNewConfig.AdID pos);
    }

    public static void getAdPos(int adType, OnAdPosResult result) {
        getAdPosReal(adType, result);
    }

    @SuppressLint("CheckResult")
    public static void getAdPosReal(int adType, OnAdPosResult result) {
        RxHttp.get(AdConst.AD_URL)
                .add("adType", adType)
                .add("brand", DeviceUtils.getManufacturer())
                .add("model", DeviceUtils.getModel())
                .add("brand", Build.BRAND)
                .add("city", gdInfo != null ? gdInfo.city : "")
                .add("province", gdInfo != null ? gdInfo.province : "")
                .add("rectangle", gdInfo != null ? gdInfo.rectangle : "")
                .add("adcode", gdInfo != null ? gdInfo.adcode : "")
                .add("ch", BuildConfig.FLAVOR)
                .add("version", BuildConfig.VERSION_CODE)
                .add("appId", BuildConfig.APPLICATION_ID)
                .toObservable(AdNewConfig.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(adNewConfig -> {
                    if (gdInfo != null && adNewConfig.data != null && !TextUtils.isEmpty(gdInfo.province) && !TextUtils.isEmpty(gdInfo.city)) {
                        adLimit = adNewConfig.limit == 1;
                    } else {
                        adLimit = true;
                    }
                    result.onResult(adNewConfig.data);
                }, err -> {
                    result.onResult(null);
                });
    }
}

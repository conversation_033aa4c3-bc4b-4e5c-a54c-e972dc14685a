//package com.dicernszh.botany.ad;
//
//import android.app.Activity;
//
//import androidx.annotation.Nullable;
//
//import com.blankj.utilcode.util.SPUtils;
//import com.bytedance.sdk.openadsdk.AdSlot;
//import com.bytedance.sdk.openadsdk.LocationProvider;
//import com.bytedance.sdk.openadsdk.TTAdConfig;
//import com.bytedance.sdk.openadsdk.TTAdConstant;
//import com.bytedance.sdk.openadsdk.TTAdNative;
//import com.bytedance.sdk.openadsdk.TTAdSdk;
//import com.bytedance.sdk.openadsdk.TTRewardVideoAd;
////import com.cttz.zjzcamera.BuildConfig;
////import com.cttz.zjzcamera.ad.AdSdk;import com.qmuiteam.qmui.widget.dialog.QMUIDialog;
////import com.freeidcard.image.BuildConfig;
////import com.freeidcard.image.ad.AdSdk;
//import com.dicernszh.botany.BuildConfig;
//import com.qmuiteam.qmui.widget.dialog.QMUIDialog;
//import com.qmuiteam.qmui.widget.dialog.QMUIDialogAction;
//import com.qq.e.ads.rewardvideo.RewardVideoAD;
//import com.qq.e.ads.rewardvideo.RewardVideoADListener;
//import com.qq.e.comm.managers.GDTAdSdk;
//import com.qq.e.comm.util.AdError;
//
//
//import java.util.Map;
//
//public class AdReward {
//    private final Activity activity;
//    private AdNewConfig.AdPosId mainPosId;
//    private AdNewConfig.AdPosId fallbackPosId;
//    private int errCnt = 0;
//    private RewardVideoAD mRewardVideoAD;
//
//    public AdReward(Activity activity) {
//        this.activity = activity;
//    }
//
//    public void show(AdSdk.OnRewardBack onRewardBack, String rewardContent) {
//        new QMUIDialog.MessageDialogBuilder(activity).setMessage(rewardContent).addAction(new QMUIDialogAction("取消", new QMUIDialogAction.ActionListener() {
//            @Override
//            public void onClick(QMUIDialog dialog, int index) {
//                onRewardBack.onReward(-1);
//                dialog.dismiss();
//            }
//        })).addAction(new QMUIDialogAction("解锁", new QMUIDialogAction.ActionListener() {
//            @Override
//            public void onClick(QMUIDialog dialog, int index) {
//                dialog.dismiss();
//                onRewardBack.onReward(1);
//                AdNet.getAdPos(AdPosConst.REWARD, pos -> {
//                    if (pos != null && pos.mainPosId != null) {
//                        mainPosId = pos.mainPosId;
//                        fallbackPosId = pos.fallbackPosId;
//                        loadAd(mainPosId);
//                    }
//                });
//            }
//        })).create().show();
//    }
//
//    public void show() {
//        AdNet.getAdPos(AdPosConst.REWARD, pos -> {
//            if (pos != null && pos.mainPosId != null) {
//                mainPosId = pos.mainPosId;
//                fallbackPosId = pos.fallbackPosId;
//                loadAd(mainPosId);
//            }
//        });
//    }
//
//    private void loadQQReward(String openAppId, String posId) {
//        GDTAdSdk.init(activity, openAppId);
//        mRewardVideoAD = new RewardVideoAD(activity, posId, new RewardVideoADListener() {
//            @Override
//            public void onADLoad() {
//                mRewardVideoAD.showAD(activity);
//            }
//
//            @Override
//            public void onVideoCached() {
//            }
//
//            @Override
//            public void onADShow() {
//            }
//
//            @Override
//            public void onADExpose() {
//            }
//
//            @Override
//            public void onReward(Map<String, Object> map) {
//            }
//
//            @Override
//            public void onADClick() {
//            }
//
//            @Override
//            public void onVideoComplete() {
//            }
//
//            @Override
//            public void onADClose() {
//            }
//
//            @Override
//            public void onError(AdError adError) {
//                if (errCnt == 0) {
//                    errCnt++;
//                    loadAd(fallbackPosId);
//                }
//            }
//        });
//        mRewardVideoAD.loadAD();
//    }
//
//
//    private void loadAd(AdNewConfig.AdPosId adPosId) {
//        if (adPosId != null) {
//            if (adPosId.platform == 1) {
//                loadTTReward(adPosId.openAppId, adPosId.posId);
//            }
//            if (adPosId.platform == 2) {
//                loadQQReward(adPosId.openAppId, adPosId.posId);
//            }
//        }
//    }
//
//    private void loadTTReward(String openAppId, String posId) {
//        initTTSDK(openAppId, new TTAdSdk.InitCallback() {
//            @Override
//            public void success() {
//                TTAdNative mTTAdNative = TTAdSdk.getAdManager().createAdNative(activity);
//                AdSlot adSlot = new AdSlot.Builder()
//                        .setCodeId(posId)
//                        .setOrientation(TTAdConstant.VERTICAL)//必填参数，期望视频的播放方向：TTAdConstant.HORIZONTAL 或 TTAdConstant.VERTICAL
//                        .build();
//                mTTAdNative.loadRewardVideoAd(adSlot, new TTAdNative.RewardVideoAdListener() {
//                    @Override
//                    public void onError(int i, String s) {
//                        if (errCnt == 0) {
//                            errCnt++;
//                            loadAd(fallbackPosId);
//                        }
//                    }
//
//                    @Override
//                    public void onRewardVideoAdLoad(TTRewardVideoAd ttRewardVideoAd) {
//                        ttRewardVideoAd.showRewardVideoAd(activity);
//                    }
//
//                    @Override
//                    public void onRewardVideoCached() {
//
//                    }
//
//                    @Override
//                    public void onRewardVideoCached(TTRewardVideoAd ttRewardVideoAd) {
//
//                    }
//                });
//            }
//
//            @Override
//            public void fail(int i, String s) {
//            }
//        });
//    }
//
//
//    private void initTTSDK(String openAdId, TTAdSdk.InitCallback initCallback) {
//        if (TTAdSdk.isInitSuccess()) {
//            initCallback.success();
//        } else {
//            TTAdSdk.init(activity, new TTAdConfig.Builder()
//                    .appId(openAdId)//应用ID
//                    .useMediation(false)//开启聚合功能，默认false
//                    .supportMultiProcess(true)//开启多进程
//                    .appName(BuildConfig.APP_NAME)
//                    .useTextureView(true)
//                    .build());
//            TTAdSdk.start(new TTAdSdk.Callback() {
//                @Override
//                public void success() {
//                    initCallback.success();
//                }
//
//                @Override
//                public void fail(int code, String msg) {
//                    initCallback.fail(code, msg);
//                }
//            });
//
//        }
//    }
//}

package com.dicernszh.botany.ad;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.widget.FrameLayout;

import com.dicernszh.botany.BuildConfig;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.wrapper.param.RxHttp;

public class AdSdk {
    private static AdSdk adSdk;
    private AdConfig config;

    private boolean isAdOpen = false;

    private AdSdk() {
    }

    public interface OnRewardBack {
        void onReward(int type);

    }

    public void initAdSdk() {
        initConfigAndSDK();
    }

    public static AdSdk getInstance() {
        if (adSdk == null) {
            adSdk = new AdSdk();
        }
        return adSdk;
    }

    public boolean isAdOpen() {
        if (!isAdOpen) {
            initConfigAndSDK();
        }
        return isAdOpen;
    }

    public void showQQNative(Activity context, FrameLayout adContainer) {
//        new AdNative(context, adContainer).show();
    }

//    public void loadFeed(Activity context, int loadCnt, AdFeed.OnAdListInterface onAdListInterface) {
////        new AdFeed(context, loadCnt, onAdListInterface).loadFeed();
//    }

    public void showNewInter(Activity activity) {
//        if (!AdNet.adLimit) {
//            new AdNewInter(activity).show();
//        }
    }

    public void showReward(Activity activity, String rewardContent, OnRewardBack onRewardBack) {
//        if (!AdNet.adLimit) {
//            new AdReward(activity).show(onRewardBack, rewardContent);
//        }
    }

    public void showReward(Activity activity) {
//        if (!AdNet.adLimit) {
//            new AdReward(activity).show();
//        }
    }


    @SuppressLint("CheckResult")
    public void showBanner(Activity context, FrameLayout adContainer) {
//        new AdBanner(context, adContainer).show();
    }

    @SuppressLint("CheckResult")
    public void showInterAd(Activity context) {
//            showNewInter(context);
    }

    public void updateConfig(AdConfig config) {
        this.config = config;
    }

    @SuppressLint("CheckResult")
    private void initConfigAndSDK() {
        RxHttp.get("http://ad.lanxitech.cloud:3004/app/pos")
                .add("adType", AdPosConst.SPLASH)
                .add("ch", BuildConfig.FLAVOR)
                .add("version", BuildConfig.VERSION_CODE)
                .add("openFlag", 1)
                .add("appId", BuildConfig.APPLICATION_ID).toObservable(AdNewConfig.class).observeOn(AndroidSchedulers.mainThread()).subscribe(adNewConfig -> {
                    isAdOpen = adNewConfig != null && adNewConfig.code == 0 && adNewConfig.data != null && adNewConfig.data.mainPosId != null;
                });
    }

    @SuppressLint("CheckResult")
    public void isAdOpen(AdOpenBack openBack) {
        RxHttp.get("http://ad.lanxitech.cloud:3004/app/pos")
                .add("adType", AdPosConst.SPLASH)
                .add("ch", BuildConfig.FLAVOR)
                .add("openFlag", 1)
                .add("version", BuildConfig.VERSION_CODE)
                .add("appId", BuildConfig.APPLICATION_ID).toObservable(AdNewConfig.class).observeOn(AndroidSchedulers.mainThread()).subscribe(adNewConfig -> {
                    isAdOpen = adNewConfig != null && adNewConfig.code == 0 && adNewConfig.data != null && adNewConfig.data.mainPosId != null;
                    openBack.onBack(isAdOpen);
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        openBack.onBack(isAdOpen);
                    }
                });
    }

    public interface AdOpenBack {
        void onBack(boolean isOpen);
    }
}

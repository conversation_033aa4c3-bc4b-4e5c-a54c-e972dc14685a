//package com.dicernszh.botany.ad;
//
//import android.app.Activity;
//import android.util.DisplayMetrics;
//import android.view.View;
//import android.view.ViewGroup;
//
//import com.bytedance.sdk.openadsdk.AdSlot;
//import com.bytedance.sdk.openadsdk.CSJAdError;
//import com.bytedance.sdk.openadsdk.CSJSplashAd;
//import com.bytedance.sdk.openadsdk.TTAdConfig;
//import com.bytedance.sdk.openadsdk.TTAdNative;
//import com.bytedance.sdk.openadsdk.TTAdSdk;
////import com.syncxyz.translation.BuilderldConfig;
////import com.cttz.zjzcamera.BuildConfig;
////import com.freeidcard.image.BuildConfig;
//import com.dicernszh.botany.BuildConfig;
//import com.qq.e.ads.splash.SplashAD;
//import com.qq.e.ads.splash.SplashADListener;
//import com.qq.e.comm.managers.GDTAdSdk;
//import com.qq.e.comm.util.AdError;
////import com.scansamrt.camera.BuildConfig;
////import com.workshopxusw.zza.BuildConfig;import com.workshopxusw.zza.ad.UIUtils;
//
//
//public class AdSplash {
//    Activity context;
//    SplashLister splashLister;
//    ViewGroup adContainer;
//    AdNewConfig.AdPosId mainPosId;
//    AdNewConfig.AdPosId fallbackPosId;
//    int errCnt = 0;
//
//
//    public AdSplash(Activity context, SplashLister splashLister, ViewGroup adContainer) {
//        this.context = context;
//        this.splashLister = splashLister;
//        this.adContainer = adContainer;
//    }
//
//
//    public void showSplash() {
//        AdNet.getAdPos(AdPosConst.SPLASH, pos -> {
//            if (pos != null && pos.mainPosId.posId != null) {
//                mainPosId = pos.mainPosId;
//                fallbackPosId = pos.fallbackPosId;
//                loadAd();
//            } else {
//                splashLister.enterMain();
//            }
//        });
//    }
//
//    private void loadAd() {
//        if (mainPosId != null) {
//            if (mainPosId.platform == 1) {
//                loadTTAd(mainPosId.openAppId, mainPosId.posId);
//            }
//            if (mainPosId.platform == 2) {
//                loadQQAd(mainPosId.openAppId, mainPosId.posId);
//            }
//        }
//    }
//
//    private void loadAdFailBack() {
//        if (fallbackPosId != null) {
//            if (fallbackPosId.platform == 1) {
//                loadTTAdFailBack(fallbackPosId.openAppId, fallbackPosId.posId);
//            }
//            if (fallbackPosId.platform == 2) {
//                loadQQAdFailBack(fallbackPosId.openAppId, fallbackPosId.posId);
//            }
//        } else {
//            splashLister.enterMain();
//        }
//    }
//
//
//    private void loadQQAdFailBack(String openAppId, String posId) {
//        GDTAdSdk.init(context, openAppId);
//        SplashAD splashAD = new SplashAD(context, posId, new SplashADListener() {
//            @Override
//            public void onADDismissed() {
//                splashLister.onADDismissed();
//            }
//
//            @Override
//            public void onNoAD(AdError adError) {
//                splashLister.enterMain();
//            }
//
//            @Override
//            public void onADPresent() {
//
//            }
//
//            @Override
//            public void onADClicked() {
//
//            }
//
//            @Override
//            public void onADTick(long l) {
//
//            }
//
//            @Override
//            public void onADExposure() {
//
//            }
//
//            @Override
//            public void onADLoaded(long l) {
//            }
//        });
//        splashAD.fetchFullScreenAndShowIn(adContainer);
//    }
//
//
//    private void loadQQAd(String openAppId, String posId) {
//        GDTAdSdk.init(context, openAppId);
//        SplashAD splashAD = new SplashAD(context, posId, new SplashADListener() {
//            @Override
//            public void onADDismissed() {
//                splashLister.onADDismissed();
//            }
//
//            @Override
//            public void onNoAD(AdError adError) {
//                loadAdFailBack();
//            }
//
//            @Override
//            public void onADPresent() {
//            }
//
//            @Override
//            public void onADClicked() {
//            }
//
//            @Override
//            public void onADTick(long l) {
//            }
//
//            @Override
//            public void onADExposure() {
//            }
//
//            @Override
//            public void onADLoaded(long l) {
//            }
//        });
//        splashAD.fetchFullScreenAndShowIn(adContainer);
//    }
//
//    private void loadTTAd(String openAppId, String posId) {
//        int splashWidthPx = UIUtils.getScreenWidthInPx(context);
//        int screenHeightPx = UIUtils.getScreenHeight(context);
//        initTTSDK(openAppId, new TTAdSdk.InitCallback() {
//            @Override
//            public void success() {
//                TTAdNative mTTAdNative = TTAdSdk.getAdManager().createAdNative(context);
//                AdSlot adSlot = new AdSlot.Builder()
//                        .setCodeId(posId)
//                        .setExpressViewAcceptedSize(splashWidthPx, screenHeightPx)
//                        .build();
//                mTTAdNative.loadSplashAd(adSlot, new TTAdNative.CSJSplashAdListener() {
//
//
//
//                    @Override
//                    public void onSplashLoadSuccess() {
//
//                    }
//
//                    @Override
//                    public void onSplashLoadFail(CSJAdError csjAdError) {
//                        loadAdFailBack();
//
//                    }
//
//                    @Override
//                    public void onSplashRenderSuccess(CSJSplashAd csjSplashAd) {
//                        if (adContainer != null && !context.isFinishing() && csjSplashAd != null) {
//                            csjSplashAd.setSplashAdListener(new CSJSplashAd.SplashAdListener() {
//                                @Override
//                                public void onSplashAdShow(CSJSplashAd csjSplashAd) {
//                                }
//
//                                @Override
//                                public void onSplashAdClick(CSJSplashAd csjSplashAd) {
//                                }
//
//                                @Override
//                                public void onSplashAdClose(CSJSplashAd csjSplashAd, int i) {
//                                    splashLister.enterMain();
//                                }
//                            });
//                            View splashView = csjSplashAd.getSplashView();
//                            adContainer.removeAllViews();
//                            UIUtils.removeFromParent(splashView);
//                            adContainer.addView(splashView);
//                        }
//                    }
//
//                    @Override
//                    public void onSplashRenderFail(CSJSplashAd csjSplashAd, CSJAdError csjAdError) {
//                        loadAdFailBack();
//                    }
//                }, 3000);
//            }
//
//            @Override
//            public void fail(int i, String s) {
//                if (errCnt == 0) {
//                    errCnt++;
//                    loadTTAd(openAppId, posId);
//                } else {
//                    splashLister.enterMain();
//                }
//            }
//        });
//    }
//
//    private void loadTTAdFailBack(String openAppId, String posId) {
//        boolean mIsHalfSize = false;
//        DisplayMetrics dm = new DisplayMetrics();
//        context.getWindowManager().getDefaultDisplay().getRealMetrics(dm);
//        float splashWidthDp = UIUtils.getScreenWidthDp(context);
//        int splashWidthPx = UIUtils.getScreenWidthInPx(context);
//        int screenHeightPx = UIUtils.getScreenHeight(context);
//        float screenHeightDp = UIUtils.px2dip(context, screenHeightPx);
//        float splashHeightDp;
//        int splashHeightPx;
//        if (mIsHalfSize) {
//            splashHeightDp = screenHeightDp * 4 / 5.f;
//            splashHeightPx = (int) (screenHeightPx * 4 / 5.f);
//        } else {
//            splashHeightDp = screenHeightDp;
//            splashHeightPx = screenHeightPx;
//        }
//
//        initTTSDK(openAppId, new TTAdSdk.InitCallback() {
//            @Override
//            public void success() {
//                TTAdNative mTTAdNative = TTAdSdk.getAdManager().createAdNative(context);
//                AdSlot adSlot = new AdSlot.Builder()
//                        .setCodeId(posId)
//                        .setExpressViewAcceptedSize(splashWidthDp, splashHeightDp) // 单位是dp
//                        .setImageAcceptedSize(splashWidthPx, splashHeightPx) //
//                        .build();
//                mTTAdNative.loadSplashAd(adSlot, new TTAdNative.CSJSplashAdListener() {
//
//                    @Override
//                    public void onSplashLoadSuccess() {
//
//                    }
//
//                    @Override
//                    public void onSplashLoadFail(CSJAdError csjAdError) {
//                        splashLister.enterMain();
//                    }
//
//                    @Override
//                    public void onSplashRenderSuccess(CSJSplashAd csjSplashAd) {
//                        if (adContainer != null && !context.isFinishing()) {
//                            csjSplashAd.setSplashAdListener(new CSJSplashAd.SplashAdListener() {
//                                @Override
//                                public void onSplashAdShow(CSJSplashAd csjSplashAd) {
//                                }
//
//                                @Override
//                                public void onSplashAdClick(CSJSplashAd csjSplashAd) {
//                                }
//
//                                @Override
//                                public void onSplashAdClose(CSJSplashAd csjSplashAd, int i) {
//                                    splashLister.enterMain();
//                                }
//                            });
//                            View splashView = csjSplashAd.getSplashView();
//                            adContainer.removeAllViews();
//                            UIUtils.removeFromParent(splashView);
//                            adContainer.addView(splashView);
//                        } else {
//                            splashLister.enterMain();
//                        }
//                    }
//
//                    @Override
//                    public void onSplashRenderFail(CSJSplashAd csjSplashAd, CSJAdError csjAdError) {
//                        splashLister.enterMain();
//                    }
//                }, 3000);
//            }
//
//            @Override
//            public void fail(int i, String s) {
//                if (errCnt == 0) {
//                    errCnt++;
//                    if (fallbackPosId != null) {
//                        loadTTAdFailBack(openAppId, posId);
//                    } else {
//                        splashLister.enterMain();
//                    }
//                } else {
//                    splashLister.enterMain();
//                }
//            }
//        });
//    }
//
//
//    private void initTTSDK(String openAdId, TTAdSdk.InitCallback initCallback) {
//        if (TTAdSdk.isInitSuccess()) {
//            initCallback.success();
//        } else {
//            TTAdSdk.init(context, new TTAdConfig.Builder()
//                    .appId(openAdId)//应用ID
//                    .useMediation(false)//开启聚合功能，默认false
//                    .supportMultiProcess(true)//开启多进程
//                    .appName(BuildConfig.APP_NAME)
//                    .useTextureView(true)
//                    .build());
//            TTAdSdk.start(new TTAdSdk.Callback() {
//                @Override
//                public void success() {
//                    initCallback.success();
//                }
//
//                @Override
//                public void fail(int code, String msg) {
//                    initCallback.fail(code, msg);
//                }
//            });
//
//        }
//    }
//
//    public interface SplashLister {
//        public void enterMain();
//
//        public void onADDismissed();
//    }
//
//}

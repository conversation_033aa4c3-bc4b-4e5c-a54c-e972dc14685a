package com.dicernszh.botany.ad;

import android.content.Context;
import android.telephony.TelephonyManager;

public class SimCardChecker {

    public static boolean isSimCardInserted(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        boolean isIn = telephonyManager.getSimState()  == TelephonyManager.SIM_STATE_READY;
        return  isIn;
    }
}

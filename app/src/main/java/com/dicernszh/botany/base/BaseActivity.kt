package com.dicernszh.botany.base

import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.dicernszh.botany.R

/**
 * Base activity that handles status bar issues
 * All activities should extend this class to avoid being hidden behind the status bar
 */
open class BaseActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        setupStatusBar()
    }

    /**
     * Setup status bar to ensure content isn't hidden behind it
     * Uses white background with dark text by default
     */
    protected fun setupStatusBar() {
        // Make the status bar transparent and allow drawing under it
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Clear translucent flag and set system bar background flag
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        
        // Set status bar color to white (default)
        setStatusBarColor(R.color.colorWhite)
        
        // Use WindowInsetsController to control system UI
        val windowInsetsController = ViewCompat.getWindowInsetsController(window.decorView)
        windowInsetsController?.apply {
            // Show status bars
            show(WindowInsetsCompat.Type.statusBars())
            // Set status bar text color to dark for white background
            isAppearanceLightStatusBars = true
        }
    }
    
    /**
     * Set the status bar color using a color resource
     * @param colorRes The color resource ID
     * @param lightStatusBar Whether to use light status bar icons (for dark backgrounds)
     */
    protected fun setStatusBarColor(@ColorRes colorRes: Int, lightStatusBar: Boolean? = true) {
        val color = ContextCompat.getColor(this, colorRes)
        setStatusBarColorInt(color, lightStatusBar ?: true)
    }
    
    /**
     * Set the status bar color using a color integer
     * @param color The color integer
     * @param lightStatusBar Whether to use light status bar icons (for dark backgrounds)
     */
    protected fun setStatusBarColorInt(@ColorInt color: Int, lightStatusBar: Boolean = false) {
        window.statusBarColor = color
        
        // Adjust status bar icon/text color based on background
        val windowInsetsController = ViewCompat.getWindowInsetsController(window.decorView)
        windowInsetsController?.isAppearanceLightStatusBars = !lightStatusBar
    }
}

package com.dicernszh.botany.model;

import java.util.List;

public class BodyData {
    public int person_num;
    public List<BodyInfo> person_info;

    public static class BodyInfo {
        public BodyPart body_parts;
        public Location location;
    }

    public static class Location {
        public float height;
        public float width;
        public float top;
        public float score;
        public float left;
    }

    public static class BodyPart {
        public PartInfo top_head;
        public PartInfo left_eye;
        public PartInfo right_eye;
        public PartInfo left_mouth_corner;
        public PartInfo right_mouth_corner;
        public PartInfo left_shoulder;
        public PartInfo right_shoulder;
        public PartInfo nose;
        public PartInfo right_ear;
        public PartInfo left_ear;


        public static class PartInfo {
            public float y;
            public float x;
            public float score;
        }
    }
}

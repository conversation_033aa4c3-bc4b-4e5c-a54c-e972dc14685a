package com.dicernszh.botany.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class DetectIDData(
    var image_status: String?,
    var words_result: DetectIDResult?,
    var idcard_number_type: Int?,
    var words_result_num: Int?

) : Parcelable

@Parcelize
data class DetectIDResult(
    var 住址: DetectWordResult?,
    var 公民身份号码: DetectWordResult?,
    var 出生: DetectWordResult?,
    var 姓名: DetectWordResult?,
    var 性别: DetectWordResult?,
    var 民族: DetectWordResult?
) : Parcelable

@Parcelize
data class DetectBankData(var result: DetectBankResult?) : Parcelable

@Parcelize
data class DetectBankResult(
    var bank_card_number: String?,
    var valid_date: String,
    var bank_card_type: Int?,
    var bank_name: String
) : Parcelable

@Parcelize
data class ImageDetectData(
    var result_num: Int?,
    var result: List<ImageFeature>?
) : Parcelable

@Parcelize
data class ImageLogoData(
    var result_num: Int?,
    var result: List<ImageLogoInfo>?
) : Parcelable

@Parcelize
data class ImageLogoInfo(
    var type: Int?,
    var name: String?,
    var probability: Float?
) : Parcelable


@Parcelize
data class ImageDishData(
    var result_num: Int?,
    var result: List<ImageDishInfo>?
) : Parcelable

@Parcelize
data class ImageDishInfo(
    var calorie: String?,
    var name: String?,
    var has_calorie: Boolean?,
    var probability: Float?,
    var baike_info: BaiKeInfo?
) : Parcelable

@Parcelize
data class ImageCurrencyData(
    var result_num: Int?,
    var result: ImageCurrencyInfo
) : Parcelable

@Parcelize
data class ImageCurrencyInfo(
    var currencyName: String,
    var currencyCode: String,
    var year: String,
    var currencyDenomination: String
) : Parcelable

@Parcelize
data class ImageLandmarkData(
    var result_num: Int?,
    var result: ImageLandmarkInfo
) : Parcelable

@Parcelize
data class ImageLandmarkInfo(
    var landmark: String
) : Parcelable


@Parcelize
data class ImageFeature(
    var score: Float?,
    var root: String?,
    var baike_info: BaiKeInfo?,
    var keyword: String?
) : Parcelable


@Parcelize
data class BaiKeInfo(
    var baike_url: String? = null,
    var image_url: String? = null,
    var description: String? = null
) : Parcelable


@Parcelize
data class DetectWordData(
    var words_result: MutableList<DetectWordResult>?,
    var words_result_num: Int?
) : Parcelable

@Parcelize
data class DetectWordResult(
    var words: String?
) : Parcelable


@Parcelize
data class DetectQRData(
    var codes_result: MutableList<DetectQRResult>?,
    var codes_result_num: Int?
) : Parcelable

@Parcelize
data class DetectQRResult(
    var type: String,
    var text: MutableList<String>?
) : Parcelable

@Parcelize
data class DetectAnimalData(
    var result: MutableList<DetectAnimalResult>?
) : Parcelable

@Parcelize
data class DetectAnimalResult(
    var score: String,
    var name: String
) : Parcelable


@Parcelize
data class DetectPlantData(
    var result: MutableList<DetectPlantResult>?
) : Parcelable

@Parcelize
data class DetectPlantResult(
    var score: String,
    var name: String
) : Parcelable

@Parcelize
data class DetectIngredientData(
    var result: MutableList<DetectIngredientResult>?
) : Parcelable

@Parcelize
data class DetectIngredientResult(
    var score: String,
    var name: String
) : Parcelable

@Parcelize
data class DetectFormData(
    var result: DetectFormResult
) : Parcelable

@Parcelize
data class DetectFormResult(
    var result_data: String,
    var percent: String,
    var request_id: String,
    var ret_code: Int,
    var ret_msg: String
) : Parcelable

@Parcelize
data class DetectFormDataReal(
    var file_url: String
) : Parcelable


@Parcelize
data class DetectDriverData(
    var words_result: DetectResultData
) : Parcelable

@Parcelize
data class DetectResultData(
    var 姓名: DetectResultDataCell?,
    var 至: DetectResultDataCell?,
    var 出生日期: DetectResultDataCell?,
    var 证号: DetectResultDataCell?,
    var 住址: DetectResultDataCell?,
    var 初次领证日期: DetectResultDataCell?,
    var 国籍: DetectResultDataCell?,
    var 准驾车型: DetectResultDataCell?,
    var 性别: DetectResultDataCell?,
    var 有效期限: DetectResultDataCell?
) : Parcelable

@Parcelize
data class DetectResultDataCell(
    var words: String?
) : Parcelable

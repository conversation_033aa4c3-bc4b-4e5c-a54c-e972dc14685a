package com.dicernszh.botany.model;

import java.util.ArrayList;
import java.util.List;

public class ShopItem {
    public String path;
    public String id;
    public String name;
    public String icon;
    private static List<ShopItem> shopItems;


    public ShopItem(String path, String name, String icon) {
        this.path = path;
        this.name = name;
        this.icon = icon;

    }

    public static List<ShopItem> getAllItem() {

        if (shopItems == null) {
            shopItems = new ArrayList<>();
            shopItems.add(new ShopItem("taobao://h5.m.taobao.com/awp/core/detail.htm?id=19212569294", "易美图", "https://g-search1.alicdn.com/img/bao/uploaded/i4/imgextra/i4/45159054/O1CN01vSkE7j2Gkmc5nyfFM_!!0-saturn_solar.jpg_460x460Q90.jpg_.webp"));
            shopItems.add(new ShopItem("taobao://h5.m.taobao.com/awp/core/detail.htm?id=530427860394", "益好旗舰店", "https://g-search1.alicdn.com/img/bao/uploaded/i4/i1/854149508/O1CN01rqdLpG2K6iRAXzVxw_!!0-item_pic.jpg_460x460Q90.jpg_.webp"));
            shopItems.add(new ShopItem("taobao://h5.m.taobao.com/awp/core/detail.htm?id=12273882488", "东讯旗舰店", "https://g-search3.alicdn.com/img/bao/uploaded/i4/i3/709098752/O1CN01cvP3RG2EWT0ooCLvQ_!!0-item_pic.jpg_460x460Q90.jpg_.webp"));
            shopItems.add(new ShopItem("taobao://h5.m.taobao.com/awp/core/detail.htm?id=563863977119", "妙语橙", "https://g-search3.alicdn.com/img/bao/uploaded/i4/i3/3319679299/O1CN016RFp2w2IYzfMyXEfC_!!3319679299.jpg_460x460Q90.jpg_.webp"));
            shopItems.add(new ShopItem("taobao://h5.m.taobao.com/awp/core/detail.htm?id=592305074723", "时间轴旗舰店", "https://g-search2.alicdn.com/img/bao/uploaded/i4/i4/1859047390/O1CN01zwUkHn24SfXut4V06_!!0-item_pic.jpg_460x460Q90.jpg_.webp"));
            shopItems.add(new ShopItem("taobao://h5.m.taobao.com/awp/core/detail.htm?id=560896803262", "卡宜旗舰店", "https://gdp.alicdn.com/imgextra/i4/2030359032/O1CN01EoUBPE2GahkVeEEDb_!!2030359032.jpg"));
            shopItems.add(new ShopItem("taobao://h5.m.taobao.com/awp/core/detail.htm?id=627429829142", "柯达时光店", "https://g-search3.alicdn.com/img/bao/uploaded/i4/i3/2204982220332/O1CN01ZniCsf1EK67NlFNac_!!2204982220332.jpg_460x460Q90.jpg_.webp"));
            shopItems.add(new ShopItem("taobao://h5.m.taobao.com/awp/core/detail.htm?id=628774151147", "汉印官方旗舰店", "https://img.alicdn.com/imgextra/i2/113530335/O1CN01bNOAew1ELTKhtj5bI_!!0-saturn_solar.jpg_220x220.jpg"));
            shopItems.add(new ShopItem("taobao://h5.m.taobao.com/awp/core/detail.htm?id=622420848546", "魁摄旗舰店", "https://img.alicdn.com/imgextra/i1/128709444/O1CN01lGZmD62JdP3cGPbs7_!!0-saturn_solar.jpg_220x220.jpg"));

        }
        return shopItems;
    }
}

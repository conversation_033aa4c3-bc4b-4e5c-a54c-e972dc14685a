package com.dicernszh.botany.model.db;

import android.content.Context;

import com.j256.ormlite.dao.Dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ResultHistoryDao {
    private Dao<ResultHistoryTableBean, Integer> dao = null;

    public ResultHistoryDao(Context context) {
        try {
            this.dao = DatabaseHelper.getInstance(context).getDao(ResultHistoryTableBean.class);
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }

    public void clear() {
        try {
            List<ResultHistoryTableBean> list = dao.queryForAll();
            for (ResultHistoryTableBean item : list) {
                dao.deleteById(item.getId());
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }


    public void insertHistoryItem(ResultHistoryTableBean historyTableBean) {
        try {
            dao.createIfNotExists(historyTableBean);
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }

    public List<ResultHistoryTableBean> queryHistoryByType(int type) {
        List<ResultHistoryTableBean> historyTableList = null;
        try {
            historyTableList = dao.queryForEq("type", type);
        } catch (SQLException throwables) {
            throwables.printStackTrace();
            historyTableList = new ArrayList<>();
        }
        return historyTableList;
    }

}

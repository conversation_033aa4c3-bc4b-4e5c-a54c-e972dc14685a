package com.dicernszh.botany.model.db;

import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;

@DatabaseTable(tableName = "tb_result_history")
public class ResultHistoryTableBean {
    @DatabaseField(columnName = "id", generatedId = true, unique = true)
    private int id;
    @DatabaseField(columnName = "type")
    private int type;
    @DatabaseField(columnName = "localPath")
    private String localPath;
    @DatabaseField(columnName = "title")
    private String title;
    @DatabaseField(columnName = "result")
    private String result;
    @DatabaseField(columnName = "time")
    private long time;

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}

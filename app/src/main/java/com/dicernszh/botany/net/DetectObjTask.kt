package com.dicernszh.botany.net

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.BitmapFactory
import android.text.TextUtils
import androidx.lifecycle.LifecycleOwner
import com.dicernszh.botany.model.Image2Txt
import com.dicernszh.botany.model.TokenData
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import rxhttp.wrapper.param.RxHttp
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import java.io.File

interface DetectObjBack<T> {
    fun onSuccess(data: T?)
    fun onFail(msg: String?)
}

class DetectObjTask<T> {
    private var filePath: String?
    private var image2Txt: com.dicernszh.botany.model.Image2Txt? = null
    private var context: Activity
    var tipDialog: QMUITipDialog? = null
    private var detectBack: DetectObjBack<T>? = null


    constructor(context: Activity, filePath: String?, image2Txt: com.dicernszh.botany.model.Image2Txt?) {
        this.context = context
        this.filePath = filePath
        this.image2Txt = image2Txt
    }

    constructor(context: Activity, filePath: String) {
        this.context = context
        this.filePath = filePath
    }

    fun start(detectBack: DetectObjBack<T>?, clazz: Class<T>) {
        this.detectBack = detectBack
        if (!TextUtils.isEmpty(filePath)) {
            compressImage(filePath, clazz)
        } else {
            detectBack?.onFail("图片不能为空")
        }
    }

    private fun compressImage(resultPath: String?, clazz: Class<T>) {
        tipDialog = QMUITipDialog.Builder(context).setTipWord("图片检测中")
            .setIconType(QMUITipDialog.Builder.ICON_TYPE_LOADING).create()
        Luban.with(context).load(File(resultPath)).setCompressListener(object : OnCompressListener {
            override fun onStart() {
                tipDialog?.show()
            }

            override fun onSuccess(p0: Int, file: File?) {
                getToken(file!!.absolutePath, clazz)

            }

            override fun onError(p0: Int, p1: Throwable?) {
                tipDialog?.dismiss()

            }

        }).launch()
    }

    @SuppressLint("CheckResult")
    private fun getToken(sourcePath: String, clazz: Class<T>) {
        RxHttp.get(Url.getImageToObjectTokenUrl())
            .toObservable(TokenData::class.java)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).subscribe(
                { tokenData -> detectImage(tokenData.access_token, sourcePath, clazz) },
                { tipDialog?.dismiss() })
    }

    @SuppressLint("CheckResult")
    private fun detectImage(token: String, sourcePath: String, clazz: Class<T>) {
        val bitmap64Str =
            com.dicernszh.botany.net.Base64Utils.convertBitmapToBase64(
                BitmapFactory.decodeFile(sourcePath)
            )
        RxHttp.postForm(image2Txt?.url + "?access_token=" + token)
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .add("id_card_side", "front")
            .add("image", bitmap64Str)
            .toObservable(clazz)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())?.subscribe({ result ->
                tipDialog?.dismiss()
                detectBack?.onSuccess(result)
            }, { tipDialog?.dismiss() })
    }
}
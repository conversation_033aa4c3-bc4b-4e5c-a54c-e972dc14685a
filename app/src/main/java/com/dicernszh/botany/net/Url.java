package com.dicernszh.botany.net;

import com.dicernszh.botany.model.Image2Txt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Random;

import rxhttp.wrapper.annotation.DefaultDomain;


public class Url {
    String APP_KEY = "7o50OrpmLwD9s0G4mCplwLFp";
    String APP_SEC = "GgUdqN9IkkxZz1jb0ppw6v0iNEvD97Rz";
    @DefaultDomain() //设置为默认域名
    public static String baseUrl = "https://aip.baidubce.com";
    public static final String genToken = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=7o50OrpmLwD9s0G4mCplwLFp&client_secret=GgUdqN9IkkxZz1jb0ppw6v0iNEvD97Rz";
    public static final String detectBody = "https://aip.baidubce.com/rest/2.0/image-classify/v1/body_analysis";
    public static final String segImg = "https://aip.baidubce.com/rest/2.0/image-classify/v1/body_seg";

    //通用文字识别（标准版）
    public static final String general_basic = "https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic";
    //网络图片文字识别
    public static final String webimage = "https://aip.baidubce.com/rest/2.0/ocr/v1/webimage";
    //网络图片文字识别（含位置版）
    public static final String webimage_loc = "https://aip.baidubce.com/rest/2.0/ocr/v1/webimage_loc";
    //身份证识别
    public static final String idcard = "https://aip.baidubce.com/rest/2.0/ocr/v1/idcard";
    //银行卡识别
    public static final String bankcard = "https://aip.baidubce.com/rest/2.0/ocr/v1/bankcard";
    //手写字
    public static final String handwriting = "https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting";
    //二维码识别
    public static final String qrcode = "https://aip.baidubce.com/rest/2.0/ocr/v1/qrcode";
    //驾驶证识别
    public static final String driving_license = "https://aip.baidubce.com/rest/2.0/ocr/v1/driving_license";
    //表格识别异步
    public static final String form_ocr_async = "https://aip.baidubce.com/rest/2.0/solution/v1/form_ocr/request";
    //获取表格识别内容
    public static final String get_form_ocr_result = "https://aip.baidubce.com/rest/2.0/solution/v1/form_ocr/get_request_result";
    //表格识别同步
    public static final String form_ocr_sync = "https://aip.baidubce.com/rest/2.0/ocr/v1/form";


    public static HashMap<String, String> textUrlMap = new HashMap<>();
    public static List<Image2Txt> accurateList = new ArrayList<>();


    static {
        accurateList.add(new Image2Txt(bankcard, "银行卡"));
        accurateList.add(new Image2Txt(idcard, "身份证"));
        accurateList.add(new Image2Txt(webimage, "拍照识字"));
        accurateList.add(new Image2Txt(qrcode, "二维码"));
        accurateList.add(new Image2Txt(handwriting, "手写字"));
    }

    public static Image2Txt getGeneral_basic() {
        return new Image2Txt(detectImage, "通用");
    }

    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //通用物体识别
    public static final String detectImage = "https://aip.baidubce.com/rest/2.0/image-classify/v2/advanced_general";
    //动物
    public static final String animal = "https://aip.baidubce.com/rest/2.0/image-classify/v1/animal";
    //植物
    public static final String plant = "https://aip.baidubce.com/rest/2.0/image-classify/v1/plant";
    //logo
    public static final String logo = "https://aip.baidubce.com/rest/2.0/image-classify/v2/logo";
    //果蔬
    public static final String ingredient = "https://aip.baidubce.com/rest/2.0/image-classify/v1/classify/ingredient";
    //菜品识别
    public static final String dish = "https://aip.baidubce.com/rest/2.0/image-classify/v2/dish";
    //红酒识别
    public static final String redwine = "https://aip.baidubce.com/rest/2.0/image-classify/v1/redwine";
    //货币识别
    public static final String currency = "https://aip.baidubce.com/rest/2.0/image-classify/v1/currency";
    //地标识别
    public static final String landmark = "https://aip.baidubce.com/rest/2.0/image-classify/v1/landmark";

    public static String getGenTokenUrl(String client_id, String client_secret) {
        return String.format("https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=%s&client_secret=%s", client_id, client_secret);
    }


    public static String getImageToTextTokenUrl() {
        return getGenTokenUrl("UWGwfjNoglgfA8cuX0enfkc6", "DHbhelzooZgcbp0qrVLsW4sCYPDNaNFZ");
    }

    public static String getImageToObjectTokenUrl() {
        Random random = new Random();
        if (random.nextInt() % 2 == 0) {
            return getGenTokenUrl("XtDwCVT3xbSbKhDm3YN9771r", "qLXSjmtW5GsMxBXS8q9g8b82II58Vr3d");
        }
        return getGenTokenUrl("RuppVUvRLrlmGNNbH5rYI9qU", "ovf5YPiKGaSOULAhKqtdKt7slqG962Lm");
    }
}
package com.dicernszh.botany.qw

import Choice
import Message
import OcrResponse
import Usage
import android.app.Activity
import android.text.TextUtils
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog
import org.json.JSONArray
import org.json.JSONObject
import okhttp3.*
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import java.io.File
import com.xquickyocrz.camera.Image2TxtResultAct
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import java.io.IOException
import java.util.concurrent.TimeUnit

interface OnDetect<T> {
    fun onSuccess(data: T?)
    fun onFail(msg: String?)
    fun onStreamData(data: String) // 流式输出的回调方法
}

class DetectAnything(private var context: Activity, filePath: String, type: Int) {
    private var filePath: String? = filePath
    private var mType: Int = type
    var tipDialog: QMUITipDialog? = null
    private var detectBack: OnDetect<OcrResponse>? = null

    // OkHttp客户端 - 增加超时时间以适应长时间响应
    private val client = OkHttpClient.Builder()
        .connectTimeout(60, TimeUnit.SECONDS)
        .readTimeout(120, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()

    // 保存Call引用，以便在需要时取消请求
    private var currentCall: Call? = null

    // 用于存储流式响应的完整结果
    private val completeResponseBuilder = StringBuilder()

    fun start(detectBack: OnDetect<OcrResponse>?) {
        this.detectBack = detectBack
        if (!TextUtils.isEmpty(filePath)) {
            compressImage(filePath)
        } else {
            detectBack?.onFail("图片不能为空")
        }
    }

    private fun compressImage(resultPath: String?) {
        tipDialog = QMUITipDialog.Builder(context).setTipWord("图片检测中")
            .setIconType(QMUITipDialog.Builder.ICON_TYPE_LOADING).create()

        Luban.with(context).load(File(resultPath)).setCompressListener(object : OnCompressListener {
            override fun onStart() {
                tipDialog?.show()
            }

            override fun onSuccess(file: File) {
                if(mType < 10){
                    recognizeObject(file.absolutePath, context)
                } else {
                    recognizeDoc(file.absolutePath, context)
                }
            }

            override fun onError(e: Throwable) {
                tipDialog?.dismiss()
                detectBack?.onFail(e.message)
            }
        }).launch()
    }

    private fun recognizeDoc(sourcePath: String, context: Activity) {
        val requestBody = JSONObject().apply {
            put("model", "qwen-vl-ocr")
            put("stream", true) // 启用流式输出
            val messagesArray = JSONArray()
            val messageObject = JSONObject()
            messageObject.put("role", "user")
            val contentArray = JSONArray()
            // Add image
            val imageContent = JSONObject()
            imageContent.put("type", "image_url")
            val imageUrl = JSONObject()
            imageUrl.put("url", "data:image/png;base64,${Base64Utils.getRequestBase64FromFile(sourcePath, context)}")
            imageContent.put("image_url", imageUrl)
            contentArray.put(imageContent)
            // Add text prompt
            val textContent = JSONObject()
            textContent.put("type", "text")
            textContent.put("text", "请识别图片中的所有文本内容，并严格保持原有的排版、格式和布局结构。识别时注意以下要求：\n" +
                    "1. 准确提取所有可见文字，包括标点符号和特殊字符\n" +
                    "2. 保留原始文本的段落分隔、缩进、列表和表格结构\n" +
                    "3. 对于多列文本，请按从左到右、从上到下的阅读顺序进行识别\n" +
                    "4. 如果图片中包含图表或图形中的文字，也请一并识别\n" +
                    "5. 若图片中没有可识别的文本内容，请仅返回\"未识别到内容\"\n" +
                    "6. 不识别任何违反法律法规或不符合伦理道德的内容\n" +
                    "7. 对于模糊不清的文字，可标注[模糊]")
            contentArray.put(textContent)
            messageObject.put("content", contentArray)
            messagesArray.put(messageObject)
            put("messages", messagesArray)
        }
        sendSseRequest(requestBody)
    }

    private fun recognizeObject(sourcePath: String, context: Activity) {
        val requestBody = JSONObject().apply {
            put("model", "qwen-vl-max-latest")
            put("stream", true) // 启用流式输出
            val messagesArray = JSONArray()
            val messageObject = JSONObject()
            messageObject.put("role", "user")
            val contentArray = JSONArray()
            val textContent = JSONObject()
            textContent.put("type", "text")

            when (mType) {
                Image2TxtResultAct.RESULT_TYPE_GENERIC_ALBUM,
                Image2TxtResultAct.RESULT_TYPE_GENERIC_SHOT -> {
                    textContent.put("text", "识别并输出图片中实际存在的主体类型相关信息，提供准确、清晰、结构化的信息。")
                }
                Image2TxtResultAct.RESULT_TYPE_ANIMAL -> {
                    textContent.put("text", "请识别图片中实际存在的动物类型相关信息，提供准确、清晰、结构化的信息。\n" +
                            "\n" +
                            "  【动物识别】\n" +
                            "   如果图片中是动物，请提供：\n" +
                            "   - 动物名称和别名\n" +
                            "   - 界，门，纲，目，科，属，种\n" +
                            "   - 形态特征\n" +
                            "   - 栖息环境\n" +
                            "   - 生活习性\n" +
                            "   - 分布区域\n" +
                            "   - 繁殖方法\n" +
                            "   - 主要价值\n" +
                            "   - 相关文化\n" +
                            "   - 保护状态（如适用）\n" +
                            "注意：仅识别并输出图片中实际存在的主体类型相关信息，对于不相关的类别不进行输出。提供准确、清晰、结构化的信息。")
                }

                Image2TxtResultAct.RESULT_TYPE_FLOWER,
                Image2TxtResultAct.RESULT_TYPE_PLANT -> {
                    textContent.put("text", "请识别图片中实际存在的植物类型相关信息，提供准确、清晰、结构化的信息。\n" +
                            "\n" +
                            "   【植物识别】\n" +
                            "   如果图片中是植物，请提供：\n" +
                            "   - 植物名称和别名\n" +
                            "   - 界，门，纲，目，科，属，种\n" +
                            "   - 主要特征\n" +
                            "   - 分布范围\n" +
                            "   - 栽培技术\n" +
                            "   - 繁殖方法\n" +
                            "   - 病虫害防治\n" +
                            "   - 生长环境\n" +
                            "   - 用途价值\n" +
                            "   - 象征意义\n" +
                            "   - 同型异名\n" +
                            "   - 异型异名\n" +
                            "   - 观赏/药用特性（如适用）\n" +
                            "注意：仅识别并输出图片中实际存在的植物类型相关信息，对于不相关的类别不进行输出。提供准确、清晰、结构化的信息。")
                }
                Image2TxtResultAct.RESULT_TYPE_FRUIT -> {
                    textContent.put("text", "请识别图片中实际存在的水果类型相关信息，提供准确、清晰、结构化的信息。\n" +
                            " 【水果识别】\n" +
                            "   - 水果名称\n" +
                            "   - 每100克热量（千卡）\n" +
                            "   - 主要营养成分及含量\n" +
                            "   - 品种特点\n" +
                            "   - 甜度/口感\n" +
                            "   - 购买建议\n" +
                            "   - 储存方法\n" +
                            "注意：仅识别并输出图片中实际存在的水果类型相关信息，对于不相关的类别不进行输出。提供准确、清晰、结构化的信息。")
                }
                Image2TxtResultAct.RESULT_TYPE_FOOD -> {
                    textContent.put("text", "请识别图片中实际存在的食物类型相关信息，提供准确、清晰、结构化的信息。\n" +
                            "  【菜品识别】\n" +
                            "   如果图片中是菜品，请提供：\n" +
                            "   - 菜品名称，别名，英文名\n" +
                            "   - 每100克热量（千卡）\n" +
                            "   - 分类，口味，主要食材，调料，特色，辣味指数，流行地区\n" +
                            "   - 历史起源\n" +
                            "   - 菜品制作\n" +
                            "   - 食用疗效\n" +
                            "   - 文化特色\n" +
                            "   - 主要价值\n" +
                            "   - 适合人群\n" +
                            "   - 相关文化\n" +
                            "   - 传承发展\n" +
                            "注意：仅识别并输出图片中实际存在的食物相关信息，对于不相关的类别不进行输出。提供准确、清晰、结构化的信息。")
                }
                Image2TxtResultAct.RESULT_TYPE_LOGO -> {
                    textContent.put("text", "请识别图片中的品牌Logo，提供准确、清晰、结构化的信息。\n" +
                            "\n" +
                            "   【品牌Logo识别】\n" +
                            "   如果图片中是品牌Logo，请提供：\n" +
                            "   - 品牌名称\n" +
                            "   - 公司/组织名称\n" +
                            "   - 所属行业/领域\n" +
                            "   - Logo设计特点描述\n" +
                            "   - 品牌创立时间（如果知道）\n" +
                            "   - 品牌国家/地区起源\n" +
                            "   - 品牌市场定位/特色\n" +
                            "   - 品牌知名度/影响力\n" +
                            "   - 品牌主要产品/服务\n" +
                            "   - Logo颜色含义（如果知道）\n" +
                            "注意：仅识别并输出图片中实际存在的品牌Logo相关信息，对于不相关的内容不进行输出。提供准确、清晰、结构化的信息。")
                }
                Image2TxtResultAct.RESULT_TYPE_LANDMARK -> {
                    textContent.put("text", "请识别图片中的著名地标建筑，提供准确、清晰、结构化的信息。\n" +
                            "\n" +
                            "   【地标建筑识别】\n" +
                            "   如果图片中是地标建筑，请提供：\n" +
                            "   - 地标名称\n" +
                            "   - 所在国家/城市/地区\n" +
                            "   - 建造年代/历史\n" +
                            "   - 建筑风格/特点\n" +
                            "   - 建筑用途（历史和现在）\n" +
                            "   - 文化/历史意义\n" +
                            "   - 建筑师/设计者（如果知道）\n" +
                            "   - 建筑高度/规模/面积（如果知道）\n" +
                            "   - 旅游价值/游客信息\n" +
                            "   - 保护状态/世界遗产情况（如适用）\n" +
                            "注意：仅识别并输出图片中实际存在的地标建筑相关信息，对于不相关的内容不进行输出。提供准确、清晰、结构化的信息。")
                }
                Image2TxtResultAct.RESULT_TYPE_CURRENCY -> {
                    textContent.put("text", "请识别图片中的货币，提供准确、清晰、结构化的信息。\n" +
                            "\n" +
                            "   【货币识别】\n" +
                            "   如果图片中是货币，请提供：\n" +
                            "   - 货币名称\n" +
                            "   - 所属国家/地区\n" +
                            "   - 面额/币值\n" +
                            "   - 货币代码（如USD、EUR、CNY等）\n" +
                            "   - 发行年份/版本\n" +
                            "   - 纸币/硬币上的图案描述\n" +
                            "   - 纸币/硬币上的人物/建筑/标志\n" +
                            "   - 防伪特征（如适用）\n" +
                            "   - 货币历史/文化意义（如有）\n" +
                            "   - 当前汇率/使用范围（如果知道）\n" +
                            "注意：仅识别并输出图片中实际存在的货币相关信息，对于不相关的内容不进行输出。提供准确、清晰、结构化的信息。")
                }
                else -> {
                    textContent.put("text", "识别并输出图片中实际存在的主体类型相关信息，提供准确、清晰、结构化的信息。")
                }
            }

            contentArray.put(textContent)

            val imageContent = JSONObject()
            imageContent.put("type", "image_url")
            val imageUrl = JSONObject()
            imageUrl.put("url", "data:image/png;base64,${Base64Utils.getRequestBase64FromFile(sourcePath, context)}")
            imageContent.put("image_url", imageUrl)
            contentArray.put(imageContent)

            messageObject.put("content", contentArray)
            messagesArray.put(messageObject)
            put("messages", messagesArray)
        }
        sendSseRequest(requestBody)
    }

    private fun sendSseRequest(requestBody: JSONObject) {
        val mediaType = "application/json; charset=utf-8".toMediaTypeOrNull()
        val requestBodyOkHttp = RequestBody.create(mediaType, requestBody.toString())

        val request = Request.Builder()
            .url("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions")
            .addHeader("Authorization", "Bearer sk-f50dc7b090774022b7d917a7ac85a4fb")
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "text/event-stream") // 请求SSE流
            .post(requestBodyOkHttp)
            .build()

        // 保存call引用，以便在需要时取消
        currentCall = client.newCall(request)

        // 执行异步请求
        currentCall?.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                context.runOnUiThread {
                    tipDialog?.dismiss()
                    detectBack?.onFail("请求失败: ${e.message}")
                }
            }

            override fun onResponse(call: Call, response: Response) {
                if (!response.isSuccessful) {
                    context.runOnUiThread {
                        tipDialog?.dismiss()
                        detectBack?.onFail("HTTP 错误: ${response.code}")
                    }
                    return
                }

                // 使用改进的方式处理SSE响应
                handleSseResponse(call, response)
            }
        })
    }

    // 改进的SSE响应处理方法
    private fun handleSseResponse(call: Call, response: Response) {
        val reader = response.body?.charStream()?.buffered()
        if (reader == null) {
            context.runOnUiThread {
                tipDialog?.dismiss()
                detectBack?.onFail("响应体为空")
            }
            return
        }

        try {
            // 使用缓冲区合并短小的内容片段，减少UI更新频率
            val contentBuffer = StringBuilder()
            var line: String? = ""
            var isDone = false

            // 使用bufferedReader逐行读取，更适合处理文本内容
            while (!isDone && !call.isCanceled() && reader.readLine().also { line = it } != null) {
                // 跳过空行
                if (line.isNullOrEmpty()) {
                    continue
                }



                // 处理SSE数据行
                if (line!!.startsWith("data:")) {
                    val data = line!!.substring(5).trim()
                    context.runOnUiThread {
                        tipDialog?.dismiss()
                    }
                    if (data == "[DONE]") {
                        // 流结束标志
                        isDone = true

                        // 发送剩余缓冲区内容
                        if (contentBuffer.isNotEmpty()) {
                            val finalChunk = contentBuffer.toString()
                            contentBuffer.clear()

                            context.runOnUiThread {
                                detectBack?.onStreamData(finalChunk)
                            }
                        }

                        // 通知完成并返回完整结果
                        context.runOnUiThread {
                            tipDialog?.dismiss()
                            val responseObject = buildFinalResponse()
                            detectBack?.onSuccess(responseObject)
                        }
                    } else {
                        try {
                            // 解析JSON数据
                            val jsonData = JSONObject(data)

                            // 处理增量内容
                            if (jsonData.has("choices")) {
                                val choices = jsonData.getJSONArray("choices")
                                if (choices.length() > 0) {
                                    val choice = choices.getJSONObject(0)
                                    if (choice.has("delta")) {
                                        val delta = choice.getJSONObject("delta")
                                        if (delta.has("content")) {
                                            val content = delta.getString("content")

                                            // 添加到完整响应
                                            completeResponseBuilder.append(content)

                                            // 添加到内容缓冲区
                                            contentBuffer.append(content)

                                            // 当缓冲区积累到一定大小，或遇到句末标点时更新UI
                                            if (contentBuffer.length >= 20 ||
                                                content.contains("。") ||
                                                content.contains("\n") ||
                                                content.contains("，") && contentBuffer.length >= 10) {

                                                val bufferContent = contentBuffer.toString()
                                                contentBuffer.clear()

                                                context.runOnUiThread {
                                                    detectBack?.onStreamData(bufferContent)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            // JSON解析错误，记录但不中断流处理
                            e.printStackTrace()
                        }
                    }
                }
            }

            // 如果没有正常结束但流已关闭，处理剩余内容
            if (!isDone && !call.isCanceled()) {
                // 发送剩余缓冲区内容
                if (contentBuffer.isNotEmpty()) {
                    val remainingContent = contentBuffer.toString()

                    context.runOnUiThread {
                        detectBack?.onStreamData(remainingContent)
                    }
                }

                // 构建最终响应
                context.runOnUiThread {
                    tipDialog?.dismiss()
                    val finalResponse = buildFinalResponse()
                    if (completeResponseBuilder.isNotEmpty()) {
                        detectBack?.onSuccess(finalResponse)
                    } else {
                        detectBack?.onFail("响应流异常结束")
                    }
                }
            }
        } catch (e: Exception) {
            context.runOnUiThread {
                tipDialog?.dismiss()
                detectBack?.onFail("读取响应流失败: ${e.message}")
            }
        } finally {
            try {
                reader.close()
            } catch (e: Exception) {
                // 忽略关闭异常
            }
            response.close()
        }
    }

    // 构建最终的完整响应对象
    private fun buildFinalResponse(): OcrResponse {
        return OcrResponse(
            choices = listOf(
                Choice(
                    finish_reason = "stop",
                    index = 0,
                    logprobs = null,
                    message = Message(
                        content = completeResponseBuilder.toString(),
                        role = "assistant"
                    )
                )
            ),
            created = (System.currentTimeMillis() / 1000).toInt(),
            id = "chatcmpl-" + System.currentTimeMillis(),
            model = if (mType < 10) "qwen-vl-max-latest" else "qwen-vl-ocr",
            `object` = "chat.completion",
            system_fingerprint = null,
            usage = Usage(
                completion_tokens = 0,
                prompt_tokens = 0,
                total_tokens = 0
            )
        )
    }

    // 取消正在进行的请求
    fun cancel() {
        currentCall?.cancel()
        tipDialog?.dismiss()
    }
}
package com.dicernszh.botany.qw

import Choice
import Message
import OcrResponse
import Usage
import android.app.Activity
import android.text.TextUtils
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog
import org.json.JSONArray
import org.json.JSONObject
import okhttp3.*
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import java.io.File
import com.dicernszh.botany.Image2TxtResultAct
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import java.io.IOException
import java.util.concurrent.TimeUnit

interface OnDetect<T> {
    fun onSuccess(data: T?)
    fun onFail(msg: String?)
    fun onStreamData(data: String) // 流式输出的回调方法
}

class DetectAnything(private var context: Activity, filePath: String, type: Int) {
    private var filePath: String? = filePath
    private var mType: Int = type
    var tipDialog: QMUITipDialog? = null
    private var detectBack: OnDetect<OcrResponse>? = null

    // OkHttp客户端 - 增加超时时间以适应长时间响应
    private val client = OkHttpClient.Builder()
        .connectTimeout(60, TimeUnit.SECONDS)
        .readTimeout(120, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()

    // 保存Call引用，以便在需要时取消请求
    private var currentCall: Call? = null

    // 用于存储流式响应的完整结果
    private val completeResponseBuilder = StringBuilder()

    fun start(detectBack: OnDetect<OcrResponse>?) {
        this.detectBack = detectBack
        if (!TextUtils.isEmpty(filePath)) {
            compressImage(filePath)
        } else {
            detectBack?.onFail("图片不能为空")
        }
    }

    private fun compressImage(resultPath: String?) {
        tipDialog = QMUITipDialog.Builder(context).setTipWord("图片检测中")
            .setIconType(QMUITipDialog.Builder.ICON_TYPE_LOADING).create()

        Luban.with(context).load(File(resultPath)).setCompressListener(object : OnCompressListener {
            override fun onStart() {
                tipDialog?.show()
            }

            override fun onSuccess(index: Int, file: File) {
                if(mType < 10){
                    recognizeObject(file.absolutePath, context)
                } else {
                    recognizeDoc(file.absolutePath, context)
                }
            }

            override fun onError(index: Int, e: Throwable) {
                tipDialog?.dismiss()
                detectBack?.onFail(e.message)
            }
        }).launch()
    }

    private fun recognizeDoc(sourcePath: String, context: Activity) {
        val requestBody = JSONObject().apply {
            put("model", "qwen-vl-ocr")
            put("stream", true) // 启用流式输出
            val messagesArray = JSONArray()
            val messageObject = JSONObject()
            messageObject.put("role", "user")
            val contentArray = JSONArray()
            // Add image
            val imageContent = JSONObject()
            imageContent.put("type", "image_url")
            val imageUrl = JSONObject()
            imageUrl.put("url", "data:image/png;base64,${Base64Utils.getRequestBase64FromFile(sourcePath, context)}")
            imageContent.put("image_url", imageUrl)
            contentArray.put(imageContent)
            // Add text prompt
            val textContent = JSONObject()
            textContent.put("type", "text")
            textContent.put("text", "请识别图片中的所有文本内容，并以Markdown格式输出，严格保持原有的排版、格式和布局结构。\n\n" +
                    "## 识别要求\n" +
                    "1. **准确提取**：所有可见文字，包括标点符号和特殊字符\n" +
                    "2. **保留结构**：原始文本的段落分隔、缩进、列表和表格结构\n" +
                    "3. **阅读顺序**：多列文本按从左到右、从上到下的顺序识别\n" +
                    "4. **图表文字**：包含图表或图形中的文字\n" +
                    "5. **空内容处理**：若无可识别内容，返回\"## 识别结果\\n\\n未识别到内容\"\n" +
                    "6. **模糊标注**：对于模糊不清的文字，标注为`[模糊]`\n\n" +
                    "## 输出格式\n" +
                    "请使用以下Markdown格式：\n" +
                    "```markdown\n" +
                    "# 文本识别结果\n\n" +
                    "[识别到的文本内容，保持原有格式]\n" +
                    "```\n\n" +
                    "注意：严格按照Markdown语法格式化输出，确保结果清晰易读。")
            contentArray.put(textContent)
            messageObject.put("content", contentArray)
            messagesArray.put(messageObject)
            put("messages", messagesArray)
        }
        sendSseRequest(requestBody)
    }

    private fun recognizeObject(sourcePath: String, context: Activity) {
        val requestBody = JSONObject().apply {
            put("model", "qwen-vl-max-latest")
            put("stream", true) // 启用流式输出
            val messagesArray = JSONArray()
            val messageObject = JSONObject()
            messageObject.put("role", "user")
            val contentArray = JSONArray()
            val textContent = JSONObject()
            textContent.put("type", "text")

            when (mType) {
                Image2TxtResultAct.RESULT_TYPE_GENERIC_ALBUM,
                Image2TxtResultAct.RESULT_TYPE_GENERIC_SHOT -> {
                    textContent.put("text", "请识别图片中的主要内容并以Markdown格式输出详细信息。\n\n" +
                            "## 识别要求\n" +
                            "- 准确识别图片中的主要物体、场景或内容\n" +
                            "- 提供详细的描述和相关信息\n" +
                            "- 如果是物品，说明其用途、特征等\n" +
                            "- 如果是场景，描述环境、氛围等\n\n" +
                            "## 输出格式\n" +
                            "```markdown\n" +
                            "# 图像识别结果\n\n" +
                            "## 主要内容\n" +
                            "[识别到的主要内容]\n\n" +
                            "## 详细描述\n" +
                            "[详细的描述信息]\n\n" +
                            "## 相关信息\n" +
                            "[相关的背景知识或使用信息]\n" +
                            "```")
                }
                Image2TxtResultAct.RESULT_TYPE_ANIMAL -> {
                    textContent.put("text", "请识别图片中的动物并以Markdown格式输出详细信息。\n\n" +
                            "## 识别要求\n" +
                            "- 准确识别动物种类\n" +
                            "- 提供科学分类信息\n" +
                            "- 描述形态特征和生活习性\n" +
                            "- 包含保护状态等重要信息\n\n" +
                            "## 输出格式\n" +
                            "```markdown\n" +
                            "# 🐾 动物识别结果\n\n" +
                            "## 基本信息\n" +
                            "- **动物名称**：[中文名称]\n" +
                            "- **学名**：[拉丁学名]\n" +
                            "- **别名**：[其他常见名称]\n\n" +
                            "## 分类学信息\n" +
                            "- **界**：[界名]\n" +
                            "- **门**：[门名]\n" +
                            "- **纲**：[纲名]\n" +
                            "- **目**：[目名]\n" +
                            "- **科**：[科名]\n" +
                            "- **属**：[属名]\n" +
                            "- **种**：[种名]\n\n" +
                            "## 形态特征\n" +
                            "[详细描述外观特征]\n\n" +
                            "## 栖息环境\n" +
                            "[生活环境描述]\n\n" +
                            "## 生活习性\n" +
                            "[行为习性描述]\n\n" +
                            "## 分布区域\n" +
                            "[地理分布信息]\n\n" +
                            "## 繁殖方式\n" +
                            "[繁殖相关信息]\n\n" +
                            "## 保护状态\n" +
                            "[保护级别和现状]\n\n" +
                            "## 文化意义\n" +
                            "[相关文化背景]\n" +
                            "```")
                }

                Image2TxtResultAct.RESULT_TYPE_FLOWER,
                Image2TxtResultAct.RESULT_TYPE_PLANT -> {
                    textContent.put("text", "请识别图片中的植物并以Markdown格式输出详细信息。\n\n" +
                            "## 识别要求\n" +
                            "- 准确识别植物种类\n" +
                            "- 提供科学分类和形态特征\n" +
                            "- 包含栽培和用途信息\n" +
                            "- 描述生态价值和文化意义\n\n" +
                            "## 输出格式\n" +
                            "```markdown\n" +
                            "# 🌿 植物识别结果\n\n" +
                            "## 基本信息\n" +
                            "- **植物名称**：[中文名称]\n" +
                            "- **学名**：[拉丁学名]\n" +
                            "- **别名**：[其他常见名称]\n" +
                            "- **科属**：[科名] [属名]\n\n" +
                            "## 分类学信息\n" +
                            "- **界**：植物界\n" +
                            "- **门**：[门名]\n" +
                            "- **纲**：[纲名]\n" +
                            "- **目**：[目名]\n" +
                            "- **科**：[科名]\n" +
                            "- **属**：[属名]\n" +
                            "- **种**：[种名]\n\n" +
                            "## 形态特征\n" +
                            "### 整体特征\n" +
                            "[植物整体描述]\n\n" +
                            "### 叶片特征\n" +
                            "[叶片形状、大小、颜色等]\n\n" +
                            "### 花朵特征\n" +
                            "[花朵颜色、形状、大小等]\n\n" +
                            "### 果实特征\n" +
                            "[果实类型、特征等]\n\n" +
                            "## 生长环境\n" +
                            "- **气候要求**：[气候条件]\n" +
                            "- **土壤要求**：[土壤类型]\n" +
                            "- **光照需求**：[光照条件]\n" +
                            "- **水分需求**：[水分条件]\n\n" +
                            "## 分布范围\n" +
                            "[地理分布信息]\n\n" +
                            "## 栽培技术\n" +
                            "### 繁殖方法\n" +
                            "[繁殖方式和技巧]\n\n" +
                            "### 养护要点\n" +
                            "[日常养护注意事项]\n\n" +
                            "### 病虫害防治\n" +
                            "[常见病虫害及防治方法]\n\n" +
                            "## 用途价值\n" +
                            "### 观赏价值\n" +
                            "[观赏特点和用途]\n\n" +
                            "### 经济价值\n" +
                            "[经济用途]\n\n" +
                            "### 药用价值\n" +
                            "[药用功效，如适用]\n\n" +
                            "### 生态价值\n" +
                            "[生态作用]\n\n" +
                            "## 文化意义\n" +
                            "[象征意义和文化背景]\n" +
                            "```")
                }
                Image2TxtResultAct.RESULT_TYPE_FRUIT -> {
                    textContent.put("text", "请识别图片中的水果并以Markdown格式输出详细信息。\n\n" +
                            "## 识别要求\n" +
                            "- 准确识别水果种类和品种\n" +
                            "- 提供营养成分和热量信息\n" +
                            "- 包含选购和储存建议\n" +
                            "- 描述口感和食用方法\n\n" +
                            "## 输出格式\n" +
                            "```markdown\n" +
                            "# 🍎 水果识别结果\n\n" +
                            "## 基本信息\n" +
                            "- **水果名称**：[中文名称]\n" +
                            "- **学名**：[拉丁学名]\n" +
                            "- **别名**：[其他常见名称]\n" +
                            "- **品种**：[具体品种]\n\n" +
                            "## 营养信息\n" +
                            "- **热量**：[每100克热量] 千卡\n" +
                            "- **主要营养成分**：\n" +
                            "  - 维生素C：[含量]\n" +
                            "  - 膳食纤维：[含量]\n" +
                            "  - 糖分：[含量]\n" +
                            "  - 其他重要营养素：[列举]\n\n" +
                            "## 品质特征\n" +
                            "- **外观特征**：[颜色、形状、大小]\n" +
                            "- **口感描述**：[甜度、酸度、质地]\n" +
                            "- **香味特点**：[香气描述]\n\n" +
                            "## 选购建议\n" +
                            "- **成熟度判断**：[如何判断成熟度]\n" +
                            "- **外观选择**：[选购要点]\n" +
                            "- **最佳季节**：[上市季节]\n\n" +
                            "## 储存方法\n" +
                            "- **储存温度**：[适宜温度]\n" +
                            "- **储存环境**：[环境要求]\n" +
                            "- **保鲜期限**：[保鲜时间]\n" +
                            "- **储存技巧**：[具体方法]\n\n" +
                            "## 食用建议\n" +
                            "- **最佳食用时间**：[建议时间]\n" +
                            "- **食用方法**：[直接食用、榨汁等]\n" +
                            "- **搭配建议**：[与其他食物的搭配]\n" +
                            "- **注意事项**：[食用禁忌等]\n\n" +
                            "## 健康功效\n" +
                            "[营养价值和健康益处]\n" +
                            "```")
                }
                Image2TxtResultAct.RESULT_TYPE_FOOD -> {
                    textContent.put("text", "请识别图片中的菜品并以Markdown格式输出详细信息。\n\n" +
                            "## 识别要求\n" +
                            "- 准确识别菜品名称和类型\n" +
                            "- 提供营养和热量信息\n" +
                            "- 包含制作方法和食材\n" +
                            "- 描述文化背景和特色\n\n" +
                            "## 输出格式\n" +
                            "```markdown\n" +
                            "# 🍽️ 菜品识别结果\n\n" +
                            "## 基本信息\n" +
                            "- **菜品名称**：[中文名称]\n" +
                            "- **英文名称**：[English Name]\n" +
                            "- **别名**：[其他名称]\n" +
                            "- **菜系分类**：[所属菜系]\n\n" +
                            "## 营养信息\n" +
                            "- **热量**：约[数值] 千卡/100克\n" +
                            "- **主要营养成分**：\n" +
                            "  - 蛋白质：[含量]\n" +
                            "  - 脂肪：[含量]\n" +
                            "  - 碳水化合物：[含量]\n" +
                            "  - 其他营养素：[列举]\n\n" +
                            "## 口味特征\n" +
                            "- **主要口味**：[甜/酸/辣/咸等]\n" +
                            "- **辣味指数**：⭐⭐⭐⭐⭐ ([数值]/5)\n" +
                            "- **口感描述**：[质地、层次等]\n\n" +
                            "## 主要食材\n" +
                            "### 主料\n" +
                            "- [主要食材列表]\n\n" +
                            "### 辅料\n" +
                            "- [辅助食材列表]\n\n" +
                            "### 调料\n" +
                            "- [调料列表]\n\n" +
                            "## 制作方法\n" +
                            "### 基本步骤\n" +
                            "1. [步骤一]\n" +
                            "2. [步骤二]\n" +
                            "3. [步骤三]\n" +
                            "[...更多步骤]\n\n" +
                            "### 制作要点\n" +
                            "- [关键技巧和注意事项]\n\n" +
                            "## 文化背景\n" +
                            "- **起源地区**：[地区]\n" +
                            "- **历史起源**：[历史背景]\n" +
                            "- **流行地区**：[主要流行区域]\n" +
                            "- **文化意义**：[文化内涵]\n\n" +
                            "## 适宜人群\n" +
                            "- **推荐人群**：[适合的人群]\n" +
                            "- **注意事项**：[饮食禁忌或注意点]\n\n" +
                            "## 营养价值\n" +
                            "[健康功效和营养价值描述]\n" +
                            "```")
                }
                Image2TxtResultAct.RESULT_TYPE_LOGO -> {
                    textContent.put("text", "请识别图片中的品牌Logo并以Markdown格式输出详细信息。\n\n" +
                            "## 识别要求\n" +
                            "- 准确识别品牌名称和Logo\n" +
                            "- 提供公司和行业信息\n" +
                            "- 描述设计特点和含义\n" +
                            "- 包含品牌历史和影响力\n\n" +
                            "## 输出格式\n" +
                            "```markdown\n" +
                            "# 🏷️ 品牌Logo识别结果\n\n" +
                            "## 品牌基本信息\n" +
                            "- **品牌名称**：[品牌名称]\n" +
                            "- **公司全称**：[完整公司名称]\n" +
                            "- **英文名称**：[English Name]\n" +
                            "- **品牌口号**：[Slogan，如有]\n\n" +
                            "## 公司信息\n" +
                            "- **成立时间**：[创立年份]\n" +
                            "- **创始人**：[创始人姓名]\n" +
                            "- **总部位置**：[国家/城市]\n" +
                            "- **所属行业**：[主要行业领域]\n\n" +
                            "## Logo设计分析\n" +
                            "### 视觉特征\n" +
                            "- **主要颜色**：[颜色描述]\n" +
                            "- **设计元素**：[图形、文字等元素]\n" +
                            "- **字体风格**：[字体特点]\n" +
                            "- **整体风格**：[现代/经典/简约等]\n\n" +
                            "### 设计含义\n" +
                            "- **颜色寓意**：[颜色的象征意义]\n" +
                            "- **图形寓意**：[图形元素的含义]\n" +
                            "- **设计理念**：[整体设计思想]\n\n" +
                            "## 品牌信息\n" +
                            "- **主要产品/服务**：[核心业务]\n" +
                            "- **目标客户**：[主要客户群体]\n" +
                            "- **市场定位**：[品牌定位]\n" +
                            "- **品牌价值观**：[核心价值观]\n\n" +
                            "## 市场影响力\n" +
                            "- **全球排名**：[如果知道具体排名]\n" +
                            "- **市场份额**：[在行业中的地位]\n" +
                            "- **知名度**：[品牌知名度描述]\n" +
                            "- **主要竞争对手**：[主要竞品]\n\n" +
                            "## 发展历程\n" +
                            "- **重要里程碑**：[关键发展节点]\n" +
                            "- **Logo演变**：[Logo历史变化，如有]\n" +
                            "- **品牌故事**：[品牌背后的故事]\n\n" +
                            "## 社会责任\n" +
                            "[企业社会责任和可持续发展举措]\n" +
                            "```")
                }
                Image2TxtResultAct.RESULT_TYPE_LANDMARK -> {
                    textContent.put("text", "请识别图片中的地标建筑并以Markdown格式输出详细信息。\n\n" +
                            "## 识别要求\n" +
                            "- 准确识别地标建筑名称\n" +
                            "- 提供历史和建筑信息\n" +
                            "- 描述文化和旅游价值\n" +
                            "- 包含实用的游览信息\n\n" +
                            "## 输出格式\n" +
                            "```markdown\n" +
                            "# 🏛️ 地标建筑识别结果\n\n" +
                            "## 基本信息\n" +
                            "- **建筑名称**：[中文名称]\n" +
                            "- **英文名称**：[English Name]\n" +
                            "- **别名**：[其他称呼]\n" +
                            "- **所在位置**：[国家/省市/具体地址]\n\n" +
                            "## 建筑信息\n" +
                            "- **建造年代**：[建造时间]\n" +
                            "- **建筑师/设计者**：[设计者姓名]\n" +
                            "- **建筑风格**：[建筑风格类型]\n" +
                            "- **建筑高度**：[高度数据]\n" +
                            "- **占地面积**：[面积数据]\n" +
                            "- **主要材料**：[建筑材料]\n\n" +
                            "## 历史背景\n" +
                            "### 建造历史\n" +
                            "[建造的历史背景和过程]\n\n" +
                            "### 历史事件\n" +
                            "[与建筑相关的重要历史事件]\n\n" +
                            "### 历史变迁\n" +
                            "[建筑的历史变化和修缮]\n\n" +
                            "## 建筑特色\n" +
                            "### 外观特征\n" +
                            "[外观设计特点]\n\n" +
                            "### 内部结构\n" +
                            "[内部布局和特色]\n\n" +
                            "### 独特之处\n" +
                            "[与众不同的设计元素]\n\n" +
                            "## 功能用途\n" +
                            "- **历史用途**：[原始功能]\n" +
                            "- **现在用途**：[当前功能]\n" +
                            "- **开放情况**：[是否对公众开放]\n\n" +
                            "## 文化价值\n" +
                            "- **文化意义**：[文化象征意义]\n" +
                            "- **艺术价值**：[艺术特色和价值]\n" +
                            "- **宗教意义**：[宗教相关，如适用]\n\n" +
                            "## 保护状态\n" +
                            "- **保护级别**：[文物保护级别]\n" +
                            "- **世界遗产**：[是否为世界遗产]\n" +
                            "- **保护措施**：[具体保护措施]\n\n" +
                            "## 旅游信息\n" +
                            "### 参观指南\n" +
                            "- **开放时间**：[参观时间]\n" +
                            "- **门票信息**：[票价信息]\n" +
                            "- **最佳游览时间**：[推荐时间]\n\n" +
                            "### 交通指南\n" +
                            "- **公共交通**：[交通方式]\n" +
                            "- **自驾路线**：[自驾信息]\n\n" +
                            "### 周边景点\n" +
                            "- [附近其他值得游览的景点]\n\n" +
                            "## 有趣事实\n" +
                            "[关于这个地标的有趣故事或冷知识]\n" +
                            "```")
                }
                Image2TxtResultAct.RESULT_TYPE_CURRENCY -> {
                    textContent.put("text", "请识别图片中的货币并以Markdown格式输出详细信息。\n\n" +
                            "## 识别要求\n" +
                            "- 准确识别货币类型和面额\n" +
                            "- 提供发行国家和年份信息\n" +
                            "- 描述设计图案和防伪特征\n" +
                            "- 包含历史和文化背景\n\n" +
                            "## 输出格式\n" +
                            "```markdown\n" +
                            "# 💰 货币识别结果\n\n" +
                            "## 基本信息\n" +
                            "- **货币名称**：[货币名称]\n" +
                            "- **英文名称**：[English Name]\n" +
                            "- **货币代码**：[ISO代码，如USD、EUR、CNY]\n" +
                            "- **面额**：[具体面值]\n" +
                            "- **货币类型**：[纸币/硬币]\n\n" +
                            "## 发行信息\n" +
                            "- **发行国家/地区**：[国家或地区]\n" +
                            "- **发行机构**：[中央银行或发行机构]\n" +
                            "- **发行年份**：[发行时间]\n" +
                            "- **版本系列**：[版本或系列名称]\n\n" +
                            "## 设计特征\n" +
                            "### 正面设计\n" +
                            "- **主要图案**：[正面主要图案]\n" +
                            "- **人物肖像**：[如有人物，描述是谁]\n" +
                            "- **建筑景观**：[如有建筑或景观]\n" +
                            "- **文字内容**：[正面文字信息]\n\n" +
                            "### 背面设计\n" +
                            "- **主要图案**：[背面主要图案]\n" +
                            "- **象征元素**：[象征性图案]\n" +
                            "- **文字内容**：[背面文字信息]\n\n" +
                            "### 颜色特征\n" +
                            "- **主色调**：[主要颜色]\n" +
                            "- **辅助色彩**：[其他颜色]\n" +
                            "- **色彩寓意**：[颜色的象征意义]\n\n" +
                            "## 防伪特征\n" +
                            "- **水印**：[水印特征]\n" +
                            "- **安全线**：[安全线特点]\n" +
                            "- **特殊印刷**：[特殊印刷技术]\n" +
                            "- **其他防伪**：[其他防伪措施]\n\n" +
                            "## 历史文化\n" +
                            "### 历史背景\n" +
                            "[货币的历史背景]\n\n" +
                            "### 文化意义\n" +
                            "[图案的文化含义和象征意义]\n\n" +
                            "### 纪念意义\n" +
                            "[如果是纪念币，说明纪念的事件或人物]\n\n" +
                            "## 经济信息\n" +
                            "- **流通状态**：[是否仍在流通]\n" +
                            "- **使用范围**：[使用的国家或地区]\n" +
                            "- **汇率参考**：[大致汇率，如果知道]\n" +
                            "- **收藏价值**：[是否有收藏价值]\n\n" +
                            "## 有趣事实\n" +
                            "[关于这种货币的有趣故事或特殊之处]\n" +
                            "```")
                }
                else -> {
                    textContent.put("text", "请识别图片中的主要内容并以Markdown格式输出详细信息。\n\n" +
                            "## 识别要求\n" +
                            "- 准确识别图片中的主要物体或内容\n" +
                            "- 提供详细的描述和相关信息\n" +
                            "- 包含实用的背景知识\n" +
                            "- 确保信息准确可靠\n\n" +
                            "## 输出格式\n" +
                            "```markdown\n" +
                            "# 🔍 图像识别结果\n\n" +
                            "## 识别内容\n" +
                            "[描述图片中识别到的主要内容]\n\n" +
                            "## 详细信息\n" +
                            "[提供相关的详细信息和背景知识]\n\n" +
                            "## 特征描述\n" +
                            "[描述物体或内容的主要特征]\n\n" +
                            "## 相关知识\n" +
                            "[提供相关的实用知识或有趣事实]\n" +
                            "```")
                }
            }

            contentArray.put(textContent)

            val imageContent = JSONObject()
            imageContent.put("type", "image_url")
            val imageUrl = JSONObject()
            imageUrl.put("url", "data:image/png;base64,${Base64Utils.getRequestBase64FromFile(sourcePath, context)}")
            imageContent.put("image_url", imageUrl)
            contentArray.put(imageContent)

            messageObject.put("content", contentArray)
            messagesArray.put(messageObject)
            put("messages", messagesArray)
        }
        sendSseRequest(requestBody)
    }

    private fun sendSseRequest(requestBody: JSONObject) {
        val mediaType = "application/json; charset=utf-8".toMediaTypeOrNull()
        val requestBodyOkHttp = RequestBody.create(mediaType, requestBody.toString())

        val request = Request.Builder()
            .url("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions")
            .addHeader("Authorization", "Bearer sk-f50dc7b090774022b7d917a7ac85a4fb")
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "text/event-stream") // 请求SSE流
            .post(requestBodyOkHttp)
            .build()

        // 保存call引用，以便在需要时取消
        currentCall = client.newCall(request)

        // 执行异步请求
        currentCall?.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                context.runOnUiThread {
                    tipDialog?.dismiss()
                    detectBack?.onFail("请求失败: ${e.message}")
                }
            }

            override fun onResponse(call: Call, response: Response) {
                if (!response.isSuccessful) {
                    context.runOnUiThread {
                        tipDialog?.dismiss()
                        detectBack?.onFail("HTTP 错误: ${response.code}")
                    }
                    return
                }

                // 使用改进的方式处理SSE响应
                handleSseResponse(call, response)
            }
        })
    }

    // 改进的SSE响应处理方法
    private fun handleSseResponse(call: Call, response: Response) {
        val reader = response.body?.charStream()?.buffered()
        if (reader == null) {
            context.runOnUiThread {
                tipDialog?.dismiss()
                detectBack?.onFail("响应体为空")
            }
            return
        }

        try {
            // 使用缓冲区合并短小的内容片段，减少UI更新频率
            val contentBuffer = StringBuilder()
            var line: String? = ""
            var isDone = false

            // 使用bufferedReader逐行读取，更适合处理文本内容
            while (!isDone && !call.isCanceled() && reader.readLine().also { line = it } != null) {
                // 跳过空行
                if (line.isNullOrEmpty()) {
                    continue
                }



                // 处理SSE数据行
                if (line!!.startsWith("data:")) {
                    val data = line!!.substring(5).trim()
                    context.runOnUiThread {
                        tipDialog?.dismiss()
                    }
                    if (data == "[DONE]") {
                        // 流结束标志
                        isDone = true

                        // 发送剩余缓冲区内容
                        if (contentBuffer.isNotEmpty()) {
                            val finalChunk = contentBuffer.toString()
                            contentBuffer.clear()

                            context.runOnUiThread {
                                detectBack?.onStreamData(finalChunk)
                            }
                        }

                        // 通知完成并返回完整结果
                        context.runOnUiThread {
                            tipDialog?.dismiss()
                            val responseObject = buildFinalResponse()
                            detectBack?.onSuccess(responseObject)
                        }
                    } else {
                        try {
                            // 解析JSON数据
                            val jsonData = JSONObject(data)

                            // 处理增量内容
                            if (jsonData.has("choices")) {
                                val choices = jsonData.getJSONArray("choices")
                                if (choices.length() > 0) {
                                    val choice = choices.getJSONObject(0)
                                    if (choice.has("delta")) {
                                        val delta = choice.getJSONObject("delta")
                                        if (delta.has("content")) {
                                            val content = delta.getString("content")

                                            // 添加到完整响应
                                            completeResponseBuilder.append(content)

                                            // 添加到内容缓冲区
                                            contentBuffer.append(content)

                                            // 当缓冲区积累到一定大小，或遇到句末标点时更新UI
                                            if (contentBuffer.length >= 20 ||
                                                content.contains("。") ||
                                                content.contains("\n") ||
                                                content.contains("，") && contentBuffer.length >= 10) {

                                                val bufferContent = contentBuffer.toString()
                                                contentBuffer.clear()

                                                context.runOnUiThread {
                                                    detectBack?.onStreamData(bufferContent)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            // JSON解析错误，记录但不中断流处理
                            e.printStackTrace()
                        }
                    }
                }
            }

            // 如果没有正常结束但流已关闭，处理剩余内容
            if (!isDone && !call.isCanceled()) {
                // 发送剩余缓冲区内容
                if (contentBuffer.isNotEmpty()) {
                    val remainingContent = contentBuffer.toString()

                    context.runOnUiThread {
                        detectBack?.onStreamData(remainingContent)
                    }
                }

                // 构建最终响应
                context.runOnUiThread {
                    tipDialog?.dismiss()
                    val finalResponse = buildFinalResponse()
                    if (completeResponseBuilder.isNotEmpty()) {
                        detectBack?.onSuccess(finalResponse)
                    } else {
                        detectBack?.onFail("响应流异常结束")
                    }
                }
            }
        } catch (e: Exception) {
            context.runOnUiThread {
                tipDialog?.dismiss()
                detectBack?.onFail("读取响应流失败: ${e.message}")
            }
        } finally {
            try {
                reader.close()
            } catch (e: Exception) {
                // 忽略关闭异常
            }
            response.close()
        }
    }

    // 构建最终的完整响应对象
    private fun buildFinalResponse(): OcrResponse {
        return OcrResponse(
            choices = listOf(
                Choice(
                    finish_reason = "stop",
                    index = 0,
                    logprobs = null,
                    message = Message(
                        content = completeResponseBuilder.toString(),
                        role = "assistant"
                    )
                )
            ),
            created = (System.currentTimeMillis() / 1000).toInt(),
            id = "chatcmpl-" + System.currentTimeMillis(),
            model = if (mType < 10) "qwen-vl-max-latest" else "qwen-vl-ocr",
            `object` = "chat.completion",
            system_fingerprint = null,
            usage = Usage(
                completion_tokens = 0,
                prompt_tokens = 0,
                total_tokens = 0
            )
        )
    }

    // 取消正在进行的请求
    fun cancel() {
        currentCall?.cancel()
        tipDialog?.dismiss()
    }
}
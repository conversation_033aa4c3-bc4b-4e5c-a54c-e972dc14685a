import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class OcrResponse(
    val choices: List<Choice>? = null,
    val created: Int? = null,
    val id: String? = null,
    val model: String? = null,
    val `object`: String? = null,
    val system_fingerprint: String? = null, // Changed to String since Any isn't Parcelable
    val usage: Usage? = null
) : Parcelable {
    override fun describeContents(): Int = 0
}

@Parcelize
data class Choice(
    val finish_reason: String? = null,
    val index: Int? = null,
    val logprobs: String? = null, // Changed to String since Any isn't Parcelable
    val message: Message? = null
) : Parcelable

@Parcelize
data class Message(
    val content: String? = null,
    val role: String? = null
) : Parcelable

@Parcelize
data class Usage(
    val completion_tokens: Int? = null,
    val prompt_tokens: Int? = null,
    val total_tokens: Int? = null
) : Parcelable

package com.dicernszh.botany.translate;

import android.annotation.SuppressLint;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.wrapper.param.RxHttp;

import java.util.HashMap;
import java.util.Map;

public class TransApi {
    private static final String TRANS_API_HOST = "http://api.fanyi.baidu.com/api/trans/vip/translate";

    private String appid;
    private String securityKey;

    public TransApi(String appid, String securityKey) {
        this.appid = appid;
        this.securityKey = securityKey;
    }

    @SuppressLint("CheckResult")
    public void getTransResult(String query, String from, String to, Consumer<TransResult> consumer) {
        Map<String, String> params = buildParams(query, from, to);
        RxHttp.get(TRANS_API_HOST)
                .addAll(params)
                .toObservable(TransResult.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<TransResult>() {
            @Override
            public void accept(TransResult transResult) throws Throwable {
                consumer.accept(transResult);

            }
        });
    }

    private Map<String, String> buildParams(String query, String from, String to) {
        Map<String, String> params = new HashMap<String, String>();
        params.put("q", query);
        params.put("from", from);
        params.put("to", to);

        params.put("appid", appid);

        // 随机数
        String salt = String.valueOf(System.currentTimeMillis());
        params.put("salt", salt);

        // 签名
        String src = appid + query + salt + securityKey; // 加密前的原文
        params.put("sign", MD5.md5(src));

        return params;
    }
}

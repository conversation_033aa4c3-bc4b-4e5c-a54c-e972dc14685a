package com.dicernszh.botany.utils

import android.app.Activity
import android.content.Intent
import com.dicernszh.botany.Image2ObjResultAct
import com.dicernszh.botany.Image2TxtResultAct
import com.dicernszh.botany.Image2TxtResultAct.Companion.RESULT_TYPE_GOOD
import com.dicernszh.botany.Image2TxtResultAct.Companion.RESULT_TYPE_NET_WORD
import com.dicernszh.botany.model.*
import com.dicernszh.botany.model.db.ResultHistoryDao
import com.dicernszh.botany.model.db.ResultHistoryTableBean
import com.dicernszh.botany.net.DetectObjBack
import com.dicernszh.botany.net.DetectObjTask
import com.dicernszh.botany.net.DetectTxtBack
import com.dicernszh.botany.net.DetectTxtTask
import com.dicernszh.botany.v2.util.ToastUtil
import com.dicernszh.botany.model.*
import rxhttp.wrapper.utils.GsonUtil


fun detect(
    act: Activity,
    filePath: String,
    mType: Int,
    image2Txt: com.dicernszh.botany.model.Image2Txt,
    resultDao: ResultHistoryDao
) {
    when (mType) {
        Image2ObjResultAct.RESULT_TYPE_GENERIC, RESULT_TYPE_GOOD, Image2ObjResultAct.RESULT_TYPE_GENERIC_LOCAL -> {
            DetectObjTask<ImageDetectData>(act, filePath, image2Txt).start(object :
                DetectObjBack<ImageDetectData> {
                override fun onSuccess(data: ImageDetectData?) {
                    var keyword: String = "识别失败"
                    if (data?.result != null && data.result!!.isNotEmpty()) {
                        keyword = data.result!![0].keyword!!
                    }
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = keyword
                            type = mType
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        startActivity(
                            Intent(
                                this,
                                Image2ObjResultAct::class.java
                            ).apply {
                                putExtra(
                                    Image2ObjResultAct.RESULT_TYPE,
                                    mType
                                )
                                putExtra(Image2ObjResultAct.RESULT_PATH, filePath)
                                putExtra(Image2ObjResultAct.RESULT_DATA, data)
                            })
                    }

                }

                override fun onFail(msg: String?) {
                }
            }, ImageDetectData::class.java)
        }
        Image2TxtResultAct.RESULT_TYPE_BANK -> {
            DetectTxtTask<DetectBankData>(act, filePath, image2Txt).start(object :
                DetectTxtBack<DetectBankData> {
                override fun onSuccess(data: DetectBankData?) {
                    var keyword: String = "识别失败"
                    if (data?.result != null) {
                        keyword = data.result!!.bank_name
                    }
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = keyword
                            type = mType
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        startActivity(
                            Intent(
                                this,
                                Image2TxtResultAct::class.java
                            ).apply {
                                putExtra(
                                    Image2TxtResultAct.RESULT_TYPE,
                                    mType
                                )
                                putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                            })
                    }

                }

                override fun onFail(msg: String?) {
                }
            }, DetectBankData::class.java)

        }
        Image2TxtResultAct.RESULT_TYPE_ID -> {
            DetectTxtTask<DetectIDData>(act, filePath, image2Txt).start(object :
                DetectTxtBack<DetectIDData> {
                override fun onSuccess(data: DetectIDData?) {
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = "身份证"
                            type = Image2TxtResultAct.RESULT_TYPE_ID
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        startActivity(
                            Intent(
                                this,
                                Image2TxtResultAct::class.java
                            ).apply {
                                putExtra(
                                    Image2TxtResultAct.RESULT_TYPE,
                                    mType
                                )
                                putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                            })
                    }
                }

                override fun onFail(msg: String?) {
                }
            }, DetectIDData::class.java)
        }
        Image2TxtResultAct.RESULT_TYPE_HAND_WRITE, Image2TxtResultAct.RESULT_TYPE_NET_WORD -> {
            DetectTxtTask<DetectWordData>(act, filePath, image2Txt).start(object :
                DetectTxtBack<DetectWordData> {
                override fun onSuccess(data: DetectWordData?) {
                    if (data?.words_result_num!! <= 0) {
                        ToastUtil.showToast(act, "检测失败请重试")
                    } else {
                        val resultBean = ResultHistoryTableBean()
                            .apply {
                                localPath = filePath
                                title = "文字/手写字"
                                type = RESULT_TYPE_NET_WORD
                                result = GsonUtil.toJson(data)
                                time = System.currentTimeMillis()
                            }
                        resultDao?.insertHistoryItem(resultBean)
                        with(act) {
                            startActivity(
                                Intent(
                                    this,
                                    Image2TxtResultAct::class.java
                                ).apply {
                                    putExtra(
                                        Image2TxtResultAct.RESULT_TYPE,
                                        mType
                                    )
                                    putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                    putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                                })
                        }
                    }

                }

                override fun onFail(msg: String?) {
                }
            }, DetectWordData::class.java)
        }
        Image2TxtResultAct.RESULT_TYPE_QR -> {
            DetectTxtTask<DetectQRData>(act, filePath, image2Txt).start(object :
                DetectTxtBack<DetectQRData> {
                override fun onSuccess(data: DetectQRData?) {
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = "二维码"
                            type = Image2TxtResultAct.RESULT_TYPE_QR
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        startActivity(
                            Intent(
                                this,
                                Image2TxtResultAct::class.java
                            ).apply {
                                putExtra(
                                    Image2TxtResultAct.RESULT_TYPE,
                                    mType
                                )
                                putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                            })
                    }

                }

                override fun onFail(msg: String?) {
                }
            }, DetectQRData::class.java)
        }
        Image2TxtResultAct.RESULT_TYPE_FORM -> {
            DetectTxtTask<String>(act, filePath, image2Txt).start(object :
                DetectTxtBack<String> {
                override fun onSuccess(data: String?) {
                    ToastUtil.showToast(act, "服务器繁忙，稍后重试")
//                    var request_id = data?.result?.request_id
//                    ToastUtil.showToast(act, request_id)
                }

                override fun onFail(msg: String?) {
                }
            }, String::class.java)
        }
        Image2TxtResultAct.RESULT_TYPE_LICENCE -> {
            DetectTxtTask<DetectDriverData>(act, filePath, image2Txt).start(object :
                DetectTxtBack<DetectDriverData> {
                override fun onSuccess(data: DetectDriverData?) {
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = "驾驶证"
                            type = mType
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        startActivity(
                            Intent(
                                this,
                                Image2TxtResultAct::class.java
                            ).apply {
                                putExtra(
                                    Image2TxtResultAct.RESULT_TYPE,
                                    mType
                                )
                                putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                            })
                    }
                }

                override fun onFail(msg: String?) {
                }
            }, DetectDriverData::class.java)
        }

        Image2TxtResultAct.RESULT_TYPE_PLANT, Image2TxtResultAct.RESULT_TYPE_FLOWER -> {
            DetectObjTask<DetectPlantData>(act, filePath, image2Txt).start(object :
                DetectObjBack<DetectPlantData> {
                override fun onSuccess(data: DetectPlantData?) {
                    var title = image2Txt.typeName;
                    if (data?.result != null && data.result!!.size > 0) {
                        title = data.result!![0].name
                    }
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = title
                            type = mType
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        startActivity(
                            Intent(
                                this,
                                Image2ObjResultAct::class.java
                            ).apply {
                                putExtra(
                                    Image2TxtResultAct.RESULT_TYPE,
                                    mType
                                )
                                putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                            })
                    }
                }

                override fun onFail(msg: String?) {
                }
            }, DetectPlantData::class.java)
        }

        Image2TxtResultAct.RESULT_TYPE_ANIMAL -> {
            DetectObjTask<DetectAnimalData>(act, filePath, image2Txt).start(object :
                DetectObjBack<DetectAnimalData> {
                override fun onSuccess(data: DetectAnimalData?) {
                    var title = image2Txt.typeName;
                    if (data?.result != null && data.result!!.size > 0) {
                        title = data.result!![0].name
                    }
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = title
                            type = mType
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        startActivity(
                            Intent(
                                this,
                                Image2ObjResultAct::class.java
                            ).apply {
                                putExtra(
                                    Image2TxtResultAct.RESULT_TYPE,
                                    mType
                                )
                                putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                            })
                    }
                }

                override fun onFail(msg: String?) {
                }
            }, DetectAnimalData::class.java)
        }
        Image2TxtResultAct.RESULT_TYPE_FRUIT -> {
            DetectObjTask<DetectIngredientData>(act, filePath, image2Txt).start(object :
                DetectObjBack<DetectIngredientData> {
                override fun onSuccess(data: DetectIngredientData?) {
                    var title = image2Txt.typeName;
                    if (data?.result != null && data.result!!.size > 0) {
                        title = data.result!![0].name
                    }
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = title
                            type = mType
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    val intent = Intent(act, Image2ObjResultAct::class.java);
                    intent.putExtra(Image2TxtResultAct.RESULT_TYPE, mType)
                    intent.putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                    data?.let {
                        intent.putExtra(Image2TxtResultAct.RESULT_DATA, it)
                    }
                    act.startActivity(intent)

                }

                override fun onFail(msg: String?) {
                    ToastUtil.showToast(act, msg)
                }
            }, DetectIngredientData::class.java)
        }

        Image2TxtResultAct.RESULT_TYPE_LOGO -> {
            DetectObjTask<ImageLogoData>(act, filePath, image2Txt).start(object :
                DetectObjBack<ImageLogoData> {
                override fun onSuccess(data: ImageLogoData?) {
                    if (data?.result == null || data.result.isNullOrEmpty()) {
                        ToastUtil.showToast(act, "识别失败请重试")
                        return
                    }
                    var title = image2Txt.typeName;
                    if (data?.result != null && data.result!!.isNotEmpty()) {
                        title = data.result!![0].name
                    }
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = title
                            type = mType
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        startActivity(
                            Intent(
                                this,
                                Image2ObjResultAct::class.java
                            ).apply {
                                putExtra(
                                    Image2TxtResultAct.RESULT_TYPE,
                                    mType
                                )
                                putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                            })
                    }
                }

                override fun onFail(msg: String?) {
                }
            }, ImageLogoData::class.java)
        }


        Image2TxtResultAct.RESULT_TYPE_DISH -> {
            DetectObjTask<ImageDishData>(act, filePath, image2Txt).start(object :
                DetectObjBack<ImageDishData> {
                override fun onSuccess(data: ImageDishData?) {
                    var title = image2Txt.typeName;
                    if (data?.result != null && data.result!!.size > 0) {
                        title = data.result!![0].name
                    }
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = title
                            type = mType
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        try {
                            startActivity(
                                Intent(
                                    this,
                                    Image2ObjResultAct::class.java
                                ).apply {
                                    putExtra(
                                        Image2TxtResultAct.RESULT_TYPE,
                                        mType
                                    )
                                    putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                    putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                                })
                        } catch (e: Exception) {
                            ToastUtil.showToast(act, "识别失败请重试")
                        }
                    }
                }

                override fun onFail(msg: String?) {
                }
            }, ImageDishData::class.java)
        }

        Image2TxtResultAct.RESULT_TYPE_LAND -> {
            DetectObjTask<ImageLandmarkData>(act, filePath, image2Txt).start(object :
                DetectObjBack<ImageLandmarkData> {
                override fun onSuccess(data: ImageLandmarkData?) {
                    var title = image2Txt.typeName;
                    if (data?.result != null) {
                        data.result.landmark.also { title = it }
                    }
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = title
                            type = mType
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        startActivity(
                            Intent(
                                this,
                                Image2ObjResultAct::class.java
                            ).apply {
                                putExtra(
                                    Image2TxtResultAct.RESULT_TYPE,
                                    mType
                                )
                                putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                            })
                    }
                }

                override fun onFail(msg: String?) {
                }
            }, ImageLandmarkData::class.java)
        }

        Image2TxtResultAct.RESULT_TYPE_MMONEY -> {
            DetectObjTask<ImageCurrencyData>(act, filePath, image2Txt).start(object :
                DetectObjBack<ImageCurrencyData> {
                override fun onSuccess(data: ImageCurrencyData?) {
                    var title = image2Txt.typeName;
                    if (data?.result != null && data.result.currencyName != null) {
                        title = data.result.currencyName
                    }
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = title
                            type = mType
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        startActivity(
                            Intent(
                                this,
                                Image2ObjResultAct::class.java
                            ).apply {
                                putExtra(
                                    Image2TxtResultAct.RESULT_TYPE,
                                    mType
                                )
                                putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                            })
                    }
                }

                override fun onFail(msg: String?) {
                }
            }, ImageCurrencyData::class.java)
        }

        Image2TxtResultAct.RESULT_TYPE_STUDENT -> {
            DetectTxtTask<DetectWordData>(act, filePath, image2Txt).start(object :
                DetectTxtBack<DetectWordData> {
                override fun onSuccess(data: DetectWordData?) {
                    val resultBean = ResultHistoryTableBean()
                        .apply {
                            localPath = filePath
                            title = "学生证"
                            type = mType
                            result = GsonUtil.toJson(data)
                            time = System.currentTimeMillis()
                        }
                    resultDao?.insertHistoryItem(resultBean)
                    with(act) {
                        startActivity(
                            Intent(
                                this,
                                Image2TxtResultAct::class.java
                            ).apply {
                                putExtra(
                                    Image2TxtResultAct.RESULT_TYPE,
                                    mType
                                )
                                putExtra(Image2TxtResultAct.RESULT_DATA, data)
                                putExtra(Image2TxtResultAct.RESULT_PATH, filePath)
                            })
                    }
                }

                override fun onFail(msg: String?) {
                }
            }, DetectWordData::class.java)

        }
    }

}




package com.dicernszh.botany.utils

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.blankj.utilcode.util.ToastUtils
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.qmuiteam.qmui.widget.dialog.QMUIDialog
import com.qmuiteam.qmui.widget.dialog.QMUIDialogAction

object OpenGalleryUtil {
    // Keep track of pending callbacks and launchers
    private val pendingCallbacks = mutableMapOf<AppCompatActivity, (ArrayList<LocalMedia>?) -> Void>()
    private val permissionLaunchers = mutableMapOf<AppCompatActivity, ActivityResultLauncher<String>>()

    // Initialize a permission launcher for an activity
    fun registerActivity(activity: AppCompatActivity) {
        if (!permissionLaunchers.containsKey(activity)) {
            val launcher = activity.registerForActivityResult(
                ActivityResultContracts.RequestPermission()
            ) { isGranted ->
                if (isGranted) {
                    pendingCallbacks[activity]?.let { callback ->
                        openAlbum(activity, callback)
                    }
                } else {
                    ToastUtils.showLong("请允许获取相关授权")
                }
            }
            permissionLaunchers[activity] = launcher
        }
    }

    // Clean up when activity is destroyed
    fun unregisterActivity(activity: AppCompatActivity) {
        pendingCallbacks.remove(activity)
        permissionLaunchers.remove(activity)
    }

    private fun openAlbum(activity: AppCompatActivity, callback: (ArrayList<LocalMedia>?) -> Void) {
        PictureSelector.create(activity)
            .openGallery(SelectMimeType.ofImage()).setMaxSelectNum(1).isDisplayCamera(false)
            .isPreviewVideo(false)
            .setImageEngine(GlideEngine.createGlideEngine()).forResult(object :
                OnResultCallbackListener<LocalMedia> {
                override fun onResult(result: ArrayList<LocalMedia>?) {
                    callback(result)
                }
                override fun onCancel() {
                }
            })
    }

    fun chooseImage(activity: AppCompatActivity, callback: (ArrayList<LocalMedia>?) -> Void) {
        // Register the activity if not already registered
        registerActivity(activity)

        // Store the callback for later use
        pendingCallbacks[activity] = callback

        val storagePermission = if (Build.VERSION.SDK_INT < 33) {
            Manifest.permission.READ_EXTERNAL_STORAGE
        } else {
            Manifest.permission.READ_MEDIA_IMAGES
        }

        // Check if permission is already granted
        if (ContextCompat.checkSelfPermission(activity, storagePermission) ==
            PackageManager.PERMISSION_GRANTED) {
            openAlbum(activity, callback)
        } else {
            // Show permission request dialog
            val permsTips = if (Build.VERSION.SDK_INT < 33) {
                "选择图片需要获取您存储权限是否允许？"
            } else {
                "选择图片需要获取您的读取相册权限是否允许？"
            }

            QMUIDialog.MessageDialogBuilder(activity).setMessage(permsTips).addAction(
                QMUIDialogAction( "不允许"
                ) { dialog, index -> dialog.dismiss() }
            ).addAction(
                QMUIDialogAction( "允许"
                ) { dialog, index ->
                    dialog.dismiss()
                    // Request permission
                    permissionLaunchers[activity]?.launch(storagePermission)
                }
            ).create().show()
        }
    }
}
package com.dicernszh.botany.v2

import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.animation.AccelerateInterpolator
import android.view.animation.AlphaAnimation
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.fragment.app.Fragment
import com.qmuiteam.qmui.arch.QMUIFragment
import com.qmuiteam.qmui.arch.QMUIFragmentActivity
//import com.umeng.commonsdk.UMConfigure
import com.dicernszh.botany.BuildConfig
import com.dicernszh.botany.model.db.ResultHistoryDao
import com.dicernszh.botany.HistoryFragment
import com.dicernszh.botany.HomeFragment
import com.dicernszh.botany.R
import com.dicernszh.botany.ad.AdSdk
import com.dicernszh.botany.base.BaseActivity
import com.dicernszh.botany.databinding.ActMainBinding
import com.dicernszh.botany.databinding.FragmentFakeLayoutBinding
import com.dicernszh.botany.v2.fragment.MineFragment

class MainActivity : BaseActivity() {

    companion object {
        const val SAVE_POS = "save_pos"
        const val RESET_POS = "reset_pos"
    }

    private val fragments: MutableList<Fragment> = mutableListOf()
    private var selectPos: Int = 0
    var resultDao: ResultHistoryDao? = null
    
    // 添加ViewBinding
    private lateinit var binding: ActMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化ViewBinding
        binding = ActMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        selectPos = savedInstanceState?.getInt(SAVE_POS, 0) ?: 0
        resultDao = ResultHistoryDao(this)
        clearFragmentAndAdd()
        initFragment()
        initNav()
//        UMConfigure.init(
//            this,
//            BuildConfig.UMENG_APP_KEY,
//            BuildConfig.UMENG_APP_CHANNEL,
//            UMConfigure.DEVICE_TYPE_PHONE,
//            null
//        )
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        resetPos(intent)
    }

    private fun clearFragmentAndAdd() {
        fragments.forEach {
            supportFragmentManager.beginTransaction().remove(it).commit()
        }
        fragments.clear()
        fragments.add(HomeFragment())
        fragments.add(HistoryFragment())
        fragments.add(MineFragment())
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt(SAVE_POS, selectPos)
    }


    private fun initFragment() {
        val trans = supportFragmentManager.beginTransaction()
        fragments.forEachIndexed { index, fragment ->
            trans.add(R.id.fragment_container, fragment, index.toString())
            if (index == selectPos) {
                trans.show(fragment)
            } else {
                trans.hide(fragment)
            }
        }
        trans.commit()
    }

    private fun switchTab(pos: Int) {
        if (selectPos != pos) {
            supportFragmentManager.beginTransaction().hide(fragments[selectPos])
                .show(fragments[pos]).commit()
        }
        selectPos = pos
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            showInterstitialAdWhenExit()
        }
        return false
    }

    private fun showInterstitialAdWhenExit() {
AdSdk.getInstance().showInterAd(this)
        val builder = AlertDialog.Builder(this)
        builder.setTitle(getString(R.string.exit_dialog_title))
        builder.setMessage(getString(R.string.exit_dialog_message))
        builder.setCancelable(true)
        builder.setPositiveButton(
            getString(R.string.exit_dialog_positive)
        ) { dialogInterface, i -> ActivityCompat.finishAfterTransition(this) }
        builder.setNegativeButton(
            getString(R.string.cancel)
        ) { dialogInterface, i -> dialogInterface.dismiss() }
        builder.show()
    }

    private fun initNav() {
        binding.bottomNav.setOnNavigationItemSelectedListener {
            when (it.itemId) {
                R.id.lesson_tab -> {
                    switchTab(0)
                }
                R.id.find_tab -> {
                    switchTab(1)
                }
                R.id.main_tab -> {
                    switchTab(2)
                }
            }
            return@setOnNavigationItemSelectedListener true
        }
        binding.bottomNav.selectedItemId = binding.bottomNav.menu.getItem(selectPos).itemId
    }

    private fun resetPos(intent: Intent?) {
        intent?.let {
            val resetPos = it.getBooleanExtra(RESET_POS, false)
            if (resetPos) {
                binding.bottomNav.selectedItemId = binding.bottomNav.menu.getItem(2).itemId
            }
        }
    }
}

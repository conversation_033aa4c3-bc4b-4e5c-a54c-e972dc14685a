package com.dicernszh.botany.v2.fragment;

import android.content.Intent;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;

import com.qmuiteam.qmui.widget.QMUIWindowInsetLayout;
import com.qmuiteam.qmui.widget.grouplist.QMUICommonListItemView;
import com.qmuiteam.qmui.widget.grouplist.QMUIGroupListView;
import com.dicernszh.botany.BuildConfig;
import com.dicernszh.botany.R;
import com.dicernszh.botany.model.db.ResultHistoryDao;
import com.dicernszh.botany.v2.base.BaseFragment;
import com.dicernszh.botany.v2.fragment.mine.AboutUsActivity;
import com.dicernszh.botany.v2.fragment.mine.FeedbackActivity;
import com.dicernszh.botany.v2.fragment.mine.PrivacyActivity;
import com.dicernszh.botany.v2.util.ToastUtil;


public class MineFragment extends BaseFragment implements View.OnClickListener {

    QMUIGroupListView mGroupListView;
    String historyStr;
    String aboutUsStr;
    String checkUpdateStr;
    String feedBackStr;
    String clearCacheStr;
    String privatePolicyStr;
    String shareOther;
    String goodRate;
    int mainColor;

    @Override
    protected View onCreateView() {
        FrameLayout layout = (FrameLayout) LayoutInflater.from(getActivity()).inflate(R.layout.fragment_mine, null);
        mGroupListView = layout.findViewById(R.id.mine_group_list_view);
        historyStr = getString(R.string.history);
        aboutUsStr = getString(R.string.about_us);
        checkUpdateStr = getString(R.string.check_update);
        feedBackStr = getString(R.string.feedback);
        clearCacheStr = getString(R.string.clear_cache);
        privatePolicyStr = getString(R.string.private_policy);
        shareOther = getString(R.string.share_other);
        goodRate = getString(R.string.good_rate);
        mainColor = Color.parseColor("#AA12AFA0");
        initGroupListView();
        return layout;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        view.findViewById(R.id.login).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
    }


    private Drawable getTintedDrawable(int resourceId) {
        Drawable drawable = ContextCompat.getDrawable(getContext(), resourceId);
        if (drawable != null) {
            drawable = DrawableCompat.wrap(drawable.mutate());
            DrawableCompat.setTint(drawable, mainColor);
            DrawableCompat.setTintMode(drawable, PorterDuff.Mode.SRC_IN); // Changed from DST_IN to SRC_IN for better MD icon coloring
        }
        return drawable;
    }

    private void initGroupListView() {
        QMUICommonListItemView aboutUs = mGroupListView.createItemView(
                getTintedDrawable(R.drawable.outline_info_24),
                aboutUsStr,
                null,
                QMUICommonListItemView.HORIZONTAL,
                QMUICommonListItemView.ACCESSORY_TYPE_CHEVRON
        );
        aboutUs.getTextView().setTextColor(mainColor);
        QMUICommonListItemView checkUpdate = mGroupListView.createItemView(
                getTintedDrawable(R.drawable.outline_system_update_24),
                checkUpdateStr,
                null,
                QMUICommonListItemView.HORIZONTAL,
                QMUICommonListItemView.ACCESSORY_TYPE_CHEVRON
        );
        checkUpdate.getTextView().setTextColor(mainColor);
        QMUICommonListItemView feedback = mGroupListView.createItemView(
                getTintedDrawable(R.drawable.outline_feedback_24),
                feedBackStr,
                null,
                QMUICommonListItemView.HORIZONTAL,
                QMUICommonListItemView.ACCESSORY_TYPE_CHEVRON
        );
        feedback.getTextView().setTextColor(mainColor);
        QMUICommonListItemView clearCache = mGroupListView.createItemView(
                getTintedDrawable(R.drawable.outline_delete_sweep_24),
                clearCacheStr,
                null,
                QMUICommonListItemView.HORIZONTAL,
                QMUICommonListItemView.ACCESSORY_TYPE_CHEVRON
        );
        clearCache.getTextView().setTextColor(mainColor);
        QMUICommonListItemView privatePolicy = mGroupListView.createItemView(
                getTintedDrawable(R.drawable.outline_verified_user_24),
                privatePolicyStr,
                null,
                QMUICommonListItemView.HORIZONTAL,
                QMUICommonListItemView.ACCESSORY_TYPE_CHEVRON
        );
        privatePolicy.getTextView().setTextColor(mainColor);

        QMUICommonListItemView shareItem = mGroupListView.createItemView(
                getTintedDrawable(R.drawable.outline_share_24),
                shareOther,
                null,
                QMUICommonListItemView.HORIZONTAL,
                QMUICommonListItemView.ACCESSORY_TYPE_CHEVRON
        );

        shareItem.getTextView().setTextColor(mainColor);

        QMUICommonListItemView rateItem = mGroupListView.createItemView(
                getTintedDrawable(R.drawable.outline_star_rate_24),
                goodRate,
                null,
                QMUICommonListItemView.HORIZONTAL,
                QMUICommonListItemView.ACCESSORY_TYPE_CHEVRON
        );

        rateItem.getTextView().setTextColor(mainColor);

        QMUIGroupListView.newSection(getContext())
                .addItemView(aboutUs, this)
                .addItemView(checkUpdate, this)
                .addItemView(feedback, this)
                .addItemView(clearCache, this)
                .addItemView(privatePolicy, this)
                .addItemView(shareItem, this)
                .addItemView(rateItem, this)
                .addTo(mGroupListView);
        mGroupListView.removeViewAt(0);
        mGroupListView.addView(getSection(), 3);
        mGroupListView.addView(getSection(), 6);
    }


    private View getSection() {
        View section = new View(getContext());
        section.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 40));
        section.setBackgroundColor(Color.parseColor("#EEEEEE"));
        return section;
    }

    @Override
    public void onClick(View v) {
        if (v instanceof QMUICommonListItemView) {
            CharSequence text = ((QMUICommonListItemView) v).getText();
            if (text.equals(aboutUsStr)) {
                startActivity(new Intent(getActivity(), AboutUsActivity.class));
            } else if (text.equals(checkUpdateStr)) {
                ToastUtil.showToast(getActivity(), getResources().getString(R.string.last_version));
            } else if (text.equals(feedBackStr)) {
                startActivity(new Intent(getActivity(), FeedbackActivity.class));
            } else if (text.equals(clearCacheStr)) {
                ResultHistoryDao dao = new ResultHistoryDao(getActivity());
                dao.clear();
                ToastUtil.showToast(getActivity(), getResources().getString(R.string.clear_history));
            } else if (text.equals(privatePolicyStr)) {
                startActivity(new Intent(getActivity(), PrivacyActivity.class));
            } else if (text.equals(shareOther)) {
                Intent textIntent = new Intent(Intent.ACTION_SEND);
                textIntent.setType("text/plain");
                textIntent.putExtra(Intent.EXTRA_TEXT, getResources().getString(R.string.app_name) + " " +
                        getResources().getString(R.string.share_app_message));
                startActivity(Intent.createChooser(textIntent, getResources().getString(R.string.share_app_title)));
            } else if (text.equals(goodRate)) {
                try {
                    Intent i = new Intent(Intent.ACTION_VIEW);
                    // Launch directly to Google Play Store using package name
                    i.setData(Uri.parse("market://details?id=" + getActivity().getPackageName()));
                    // If Play Store app is not available, open in browser
                    i.addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY | Intent.FLAG_ACTIVITY_NEW_DOCUMENT | Intent.FLAG_ACTIVITY_MULTIPLE_TASK);
                    startActivity(i);
                } catch (Exception e) {
                    // If market app failed, try with browser
                    try {
                        Intent i = new Intent(Intent.ACTION_VIEW,
                                Uri.parse("https://play.google.com/store/apps/details?id=" + getActivity().getPackageName()));
                        startActivity(i);
                    } catch (Exception e2) {
                        Toast.makeText(getActivity(), getResources().getString(R.string.cannot_open_store), Toast.LENGTH_SHORT).show();

                        e2.printStackTrace();
                    }
                }
            }
        }
    }
}
package com.dicernszh.botany.v2.fragment.mine;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.dicernszh.botany.R;
import com.dicernszh.botany.base.BaseActivity;
import com.dicernszh.botany.v2.util.AppUtil;
import com.qmuiteam.qmui.util.QMUIPackageHelper;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;

/**
 * About Us Activity - converted from AboutUsFragment
 */
public class AboutUsActivity extends BaseActivity {
    private QMUITopBarLayout mTopBar;
    private ImageView mAppIcon;
    private TextView mVersionTextView;
    private String version;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.fragment_about_us);
        
        // Set status bar color to white

        mTopBar = findViewById(R.id.about_us_topbar);
        mAppIcon = findViewById(R.id.app_icon);
        mVersionTextView = findViewById(R.id.version);
        version = getString(R.string.version);
        
        initTopBar();
        
        // Set app icon
        Drawable appDrawable = AppUtil.getAppIcon(this, getPackageName());
        if (appDrawable != null) {
            mAppIcon.setImageDrawable(appDrawable);
        } else {
            mAppIcon.setImageResource(R.mipmap.ic_logo);
        }
        
        // Set version info
        mVersionTextView.setText(String.format("%s%s", version, QMUIPackageHelper.getAppVersion(this)));
    }

    private void initTopBar() {
        mTopBar.addLeftBackImageButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mTopBar.setTitle(getResources().getString(R.string.about_us));
    }
}

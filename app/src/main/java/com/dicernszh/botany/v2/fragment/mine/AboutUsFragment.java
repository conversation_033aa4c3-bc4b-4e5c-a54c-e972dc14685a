package com.dicernszh.botany.v2.fragment.mine;

import android.app.Activity;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.dicernszh.botany.R;
import com.dicernszh.botany.v2.base.BaseFragment;
import com.dicernszh.botany.v2.util.AppUtil;
import com.qmuiteam.qmui.util.QMUIPackageHelper;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;


public class AboutUsFragment extends BaseFragment {
    QMUITopBarLayout mTopBar;
    ImageView mAppIcon;
    TextView mVersionTextView;
    String version;

    @Override
    protected View onCreateView() {
        View root = LayoutInflater.from(getActivity()).inflate(R.layout.fragment_about_us, null);
        mTopBar = root.findViewById(R.id.about_us_topbar);
        mAppIcon = root.findViewById(R.id.app_icon);
        mVersionTextView = root.findViewById(R.id.version);
        version = getString(R.string.version);
        initTopBar();
        Activity activity = getActivity();
        if (activity != null) {
            Drawable appDrawable = AppUtil.getAppIcon(activity, activity.getPackageName());
            if (appDrawable != null) {
                mAppIcon.setImageDrawable(appDrawable);
            } else {
                mAppIcon.setImageResource(R.mipmap.ic_logo);
            }
        }
        mVersionTextView.setText(String.format("%s%s", version, QMUIPackageHelper.getAppVersion(getContext())));
        return root;
    }

    @Override
    protected void onBackPressed() {
        super.onBackPressed();
    }

    private void initTopBar() {
        mTopBar.addLeftBackImageButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getBaseFragmentActivity().popBackStack();
            }
        });
        mTopBar.setTitle(getResources().getString(R.string.about_us));
    }

}

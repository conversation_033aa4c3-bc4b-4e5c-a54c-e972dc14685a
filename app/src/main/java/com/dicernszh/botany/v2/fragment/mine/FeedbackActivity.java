package com.dicernszh.botany.v2.fragment.mine;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;

import androidx.annotation.Nullable;

import com.dicernszh.botany.R;
import com.dicernszh.botany.base.BaseActivity;
import com.dicernszh.botany.v2.util.ToastUtil;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;

/**
 * Feedback Activity - converted from FeedbackFragment
 */
public class FeedbackActivity extends BaseActivity {
    private QMUITopBarLayout mTopBar;
    private EditText feedback;
    private EditText feedbackQQ;
    private Button mSubmit;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.fragment_feedback);
        
        // Set status bar color to white

        mTopBar = findViewById(R.id.feedback_topbar);
        feedback = findViewById(R.id.feedback_edit_feedback);
        feedbackQQ = findViewById(R.id.feedback_edit_qq);
        mSubmit = findViewById(R.id.feedback_btn_submit);
        
        initTopBar();
        initFeedback();
    }

    private void initTopBar() {
        mTopBar.addLeftBackImageButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        mTopBar.setTitle(getResources().getString(R.string.feedback));
    }

    private void initFeedback() {
        mSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                submitFeedback();
            }
        });
    }

    private void submitFeedback() {
        if (String.valueOf(feedback.getText()).equals("") || String.valueOf(feedbackQQ.getText()).equals("")) {
            ToastUtil.showToast(this, getResources().getString(R.string.submit_tips));
        } else {
            ToastUtil.showToast(this, getResources().getString(R.string.submit_success));
            // Close the activity after successful submission
            finish();
        }
    }
}

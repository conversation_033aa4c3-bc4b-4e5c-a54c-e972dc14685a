package com.dicernszh.botany.v2.fragment.mine;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;

import com.dicernszh.botany.R;
import com.dicernszh.botany.v2.base.BaseFragment;
import com.dicernszh.botany.v2.util.ToastUtil;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;

public class FeedbackFragment extends BaseFragment {

    QMUITopBarLayout mTopBar;
    EditText feedback;
    EditText feedbackQQ;
    Button mSubmit;

    @Override
    protected View onCreateView() {
        View root = LayoutInflater.from(getActivity()).inflate(R.layout.fragment_feedback, null);
        mTopBar = root.findViewById(R.id.feedback_topbar);
        feedback = root.findViewById(R.id.feedback_edit_feedback);
        feedbackQQ = root.findViewById(R.id.feedback_edit_qq);
        mSubmit = root.findViewById(R.id.feedback_btn_submit);
        initTopBar();
        initFeedback();
        return root;
    }

    @Override
    protected void onBackPressed() {
        getBaseFragmentActivity().popBackStack();
    }

    private void initTopBar() {
        mTopBar.addLeftBackImageButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getBaseFragmentActivity().popBackStack();
            }
        });

        mTopBar.setTitle(getResources().getString(R.string.feedback));
    }

    private void initFeedback() {
        mSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                submitFeedback();
            }
        });
    }

    private void submitFeedback() {
        if (String.valueOf(feedback.getText()).equals("") || String.valueOf(feedbackQQ.getText()).equals("")) {
            ToastUtil.showToast(getActivity(), getResources().getString(R.string.submit_tips));
        } else {
            ToastUtil.showToast(getActivity(), getResources().getString(R.string.submit_success));
        }
    }
}

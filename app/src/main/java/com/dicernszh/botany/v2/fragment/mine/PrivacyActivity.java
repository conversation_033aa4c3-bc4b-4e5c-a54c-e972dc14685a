package com.dicernszh.botany.v2.fragment.mine;

import android.os.Bundle;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;

import androidx.annotation.Nullable;

import com.dicernszh.botany.R;
import com.dicernszh.botany.UrlConst;
import com.dicernszh.botany.base.BaseActivity;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.webview.QMUIWebView;
import com.qmuiteam.qmui.widget.webview.QMUIWebViewClient;

/**
 * Privacy Policy Activity - converted from PrivatePolicyFragment
 */
public class PrivacyActivity extends BaseActivity {
    private QMUITopBarLayout mTopBar;
    private QMUIWebView mWeb;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.fragment_private_policy);
        

        mTopBar = findViewById(R.id.pp_topbar);
        mWeb = findViewById(R.id.pp_web);
        
        initTopBar();
        initWeb();
    }

    private void initTopBar() {
        mTopBar.addLeftBackImageButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mTopBar.setTitle(getResources().getString(R.string.private_policy));
    }

    private void initWeb() {
        WebSettings settings = mWeb.getSettings();
        settings.setJavaScriptEnabled(true);
        mWeb.setWebViewClient(new QMUIWebViewClient(true, true));
        mWeb.setWebChromeClient(new WebChromeClient());
        mWeb.loadUrl(UrlConst.getAppPrivacy(this));
    }
    
    @Override
    public void onBackPressed() {
        if (mWeb.canGoBack()) {
            mWeb.goBack();
        } else {
            super.onBackPressed();
        }
    }
}

package com.dicernszh.botany.v2.fragment.mine;

import android.view.LayoutInflater;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;

import com.dicernszh.botany.R;
import com.dicernszh.botany.UrlConst;
import com.dicernszh.botany.v2.base.BaseFragment;
import com.qmuiteam.qmui.widget.QMUITopBarLayout;
import com.qmuiteam.qmui.widget.webview.QMUIWebView;
import com.qmuiteam.qmui.widget.webview.QMUIWebViewClient;

public class PrivatePolicyFragment extends BaseFragment {

    QMUITopBarLayout mTopBar;
    QMUIWebView mWeb;

    @Override
    protected View onCreateView() {
        View root = LayoutInflater.from(getActivity()).inflate(R.layout.fragment_private_policy, null);
        mTopBar = root.findViewById(R.id.pp_topbar);
        mWeb = root.findViewById(R.id.pp_web);
        initTopBar();
        initWeb();
        return root;
    }

    @Override
    protected void onBackPressed() {
        getBaseFragmentActivity().popBackStack();
    }

    private void initTopBar() {
        mTopBar.addLeftBackImageButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getBaseFragmentActivity().popBackStack();
            }
        });
        mTopBar.setTitle(getResources().getString(R.string.private_policy));
    }

    private void initWeb() {
        WebSettings settings = mWeb.getSettings();
        settings.setJavaScriptEnabled(true);
        mWeb.setWebViewClient(new QMUIWebViewClient(true, true));
        mWeb.setWebChromeClient(new WebChromeClient());
        mWeb.loadUrl(UrlConst.getAppPrivacy(getActivity()));
    }
}

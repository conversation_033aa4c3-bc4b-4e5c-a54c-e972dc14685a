package com.dicernszh.botany.v2.util;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.widget.Toast;

public class ToastUtil {

    private static Toast sToast;

    @SuppressLint("ShowToast")
    public static void showToast(final Activity activity, final String message) {

        if (sToast == null) {
            sToast = Toast.makeText(activity, message, Toast.LENGTH_SHORT);
        } else {
            sToast.setText(message);
        }

        if ("main".equals(Thread.currentThread().getName())) {
            //Log.e("ToastUtil", "在主线程");
            sToast.show();
        } else {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    //Log.e("ToastUtil", "不在主线程");
                    sToast.show();
                }
            });
        }
    }

}

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.qmuiteam.qmui.widget.QMUITopBar
        android:id="@+id/top_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.qmuiteam.qmui.widget.grouplist.QMUIGroupListView
        app:layout_constraintTop_toBottomOf="@+id/top_bar"
        android:id="@+id/mine_group_list_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        />


</androidx.constraintlayout.widget.ConstraintLayout>
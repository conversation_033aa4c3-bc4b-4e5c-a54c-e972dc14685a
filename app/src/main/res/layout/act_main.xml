<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorWhite">

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="49dp" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_nav"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        app:itemIconSize="20dp"
        app:itemIconTint="@color/tab_select_color"
        app:itemTextAppearanceActive="@style/NavTextAppearance"
        app:itemTextAppearanceInactive="@style/NavTextAppearance"
        app:itemTextColor="@color/tab_select_color"
        app:labelVisibilityMode="labeled"
        app:menu="@menu/bottom_nav" />

</FrameLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
              android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.qmuiteam.qmui.widget.QMUITopBar
        android:id="@+id/top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_result"
        android:layout_width="wrap_content"
        android:layout_height="300dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="36dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="12sp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_result"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center_horizontal"
                android:lineSpacingMultiplier="1.2"
                android:textIsSelectable="true" />
        </LinearLayout>
    </ScrollView>

    <TextView
        android:id="@+id/btn_copy"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_gravity="bottom"
        android:background="@color/main_color"
        android:gravity="center"
        android:text="复制"
        android:textColor="@color/white" />
</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                   xmlns:app="http://schemas.android.com/apk/res-auto"
                                                   android:layout_width="match_parent"
                                                   android:layout_height="match_parent"
                                                   android:fitsSystemWindows="true"
                                                   android:background="@color/white">

    <com.qmuiteam.qmui.widget.QMUITopBar
            android:id="@+id/top_bar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/history_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/top_bar" />

    <com.qmuiteam.qmui.widget.QMUIEmptyView
            android:id="@+id/empty_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:qmui_detail_text="@string/history_empty_text" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              xmlns:tools="http://schemas.android.com/tools"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:background="@color/white"
              android:fitsSystemWindows="true"
              android:orientation="vertical">

    <com.qmuiteam.qmui.widget.QMUITopBar
            android:id="@+id/top_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

        <ImageView
                android:id="@+id/iv_result"
                android:layout_width="wrap_content"
                android:layout_height="152dp"
                android:layout_margin="16dp" />

        <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

            <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:fontFamily="PingFangSC"
                    android:text="@string/possible_recognition_result"
                    android:textColor="#FF666666"
                    android:textSize="14sp" />

            <TextView
                    android:id="@+id/tv_keyword"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="PingFangSC"
                    android:textColor="#FF000000"
                    android:textSize="24sp"
                    tools:text="桂花" />

        </LinearLayout>
    </LinearLayout>

    <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:text="@string/encyclopedia"
            android:textColor="#FF000000"
            android:textSize="16sp"
            android:textStyle="bold" />

    <ScrollView
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="0dp">

        <com.qmuiteam.qmui.widget.webview.QMUIWebView
                android:id="@+id/web_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
    </ScrollView>
    <TextView
            android:id="@+id/btn_copy"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_gravity="bottom"
            android:background="@color/main_color"
            android:gravity="center"
            android:text="@string/encyclopedia"
            android:textColor="@color/white" />


</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical"
              android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <com.qmuiteam.qmui.widget.QMUITopBarLayout
            android:id="@+id/top_bar"
            android:layout_width="match_parent"
            android:layout_height="48dp">

        <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

            <ImageView
                    android:padding="12dp"
                    android:id="@+id/back"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/back"
                    android:layout_width="48dp"
                    android:layout_height="48dp" />

            <ImageView
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:id="@+id/close"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/close"
                    android:layout_width="24dp"
                    android:layout_height="48dp" />

            <TextView
                    android:ellipsize="end"
                    android:layout_marginRight="16dp"
                    android:maxLines="1"
                    android:id="@+id/page_title"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:layout_width="0dp"
                    android:layout_height="match_parent" />

        </LinearLayout>
    </com.qmuiteam.qmui.widget.QMUITopBarLayout>

    <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        <com.qmuiteam.qmui.widget.webview.QMUIWebView
                android:id="@+id/web"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        <FrameLayout
                android:background="#f2f2f2"
                android:id="@+id/loading_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

            <com.qmuiteam.qmui.widget.QMUILoadingView
                    android:layout_gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
        </FrameLayout>

    </FrameLayout>


</LinearLayout>
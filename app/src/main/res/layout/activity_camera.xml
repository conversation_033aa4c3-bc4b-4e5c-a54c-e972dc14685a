<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Camera -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#000000">

        <RelativeLayout
            android:id="@+id/top_rl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/close_btn"
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:layout_alignParentLeft="true"
                android:src="@drawable/ic_close_camera" />

            <ImageView
                android:layout_width="28dp"
                android:layout_height="24dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_turn"
                android:visibility="gone" />
        </RelativeLayout>


        <com.otaliastudios.cameraview.CameraView
            android:id="@+id/camera"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:keepScreenOn="true"
            app:cameraAudio="off"
            app:cameraAutoFocusMarker="@string/cameraview_default_autofocus_marker"
            app:cameraEngine="camera2"
            app:cameraExperimental="true"
            app:cameraFacing="back"
            app:cameraFlash="off"
            app:cameraGestureLongTap="none"
            app:cameraGesturePinch="zoom"
            app:cameraGestureScrollHorizontal="filterControl1"
            app:cameraGestureScrollVertical="exposureCorrection"
            app:cameraGestureTap="autoFocus"
            app:cameraGrid="off"
            app:cameraMode="picture"
            app:cameraPlaySounds="true"
            app:cameraPreview="glSurface"
            app:layout_constraintBottom_toTopOf="@+id/btns"
            app:layout_constraintTop_toBottomOf="@+id/top_rl" />

        <RelativeLayout
            android:id="@+id/btns"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
            android:padding="16dp"
            android:weightSum="4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                        android:id="@+id/chooseImage"
                        android:layout_width="36dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:src="@drawable/shot_album"/>

                <TextView
                    android:id="@+id/tv_album"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/album"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>


            <ImageView
                android:id="@+id/capturePictureSnapshot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:background="@drawable/bg_btn_camera" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView

                    android:id="@+id/toggleCamera"
                    android:layout_width="36dp"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_flashlight" />

                <TextView
                    android:id="@+id/tv_flash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/flash_on"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>


        </RelativeLayout>


        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="centerInside"
            android:scaleX="1.1"
            android:scaleY="1.1"
            android:src="@mipmap/es"
            app:layout_constraintBottom_toBottomOf="@+id/camera"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@+id/camera"
            app:layout_constraintTop_toTopOf="@+id/camera"
            app:layout_constraintVertical_bias="0.0" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="centerInside"
            android:scaleX="1.6"
            android:scaleY="1.6"
            android:src="@drawable/ic_et"
            app:layout_constraintBottom_toBottomOf="@+id/camera"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@+id/camera"
            app:layout_constraintTop_toTopOf="@+id/camera" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <!-- Edit -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/controls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:elevation="4dp"
        app:behavior_hideable="true"
        app:behavior_peekHeight="300dp"
        app:behavior_skipCollapsed="false"
        app:layout_behavior="@string/bottom_sheet_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>

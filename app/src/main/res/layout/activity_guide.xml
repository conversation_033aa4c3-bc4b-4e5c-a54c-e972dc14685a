<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.widget.QMUIWindowInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.qmuiteam.qmui.widget.QMUITopBar
        android:id="@+id/top_bar"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:qmui_topbar_left_back_drawable_id="@mipmap/ic_back"
        app:qmui_topbar_title_color="#333333" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="54dp"
        android:layout_marginBottom="64dp"
        android:background="#f2f2f2">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <ImageView
                android:layout_width="353dp"
                android:layout_height="276dp"
                android:layout_marginLeft="11dp"
                android:src="@drawable/guide1" />

            <ImageView
                android:layout_width="353dp"
                android:layout_height="276dp"
                android:layout_marginLeft="11dp"
                android:src="@drawable/guide2" />

            <ImageView
                android:layout_width="353dp"
                android:layout_height="276dp"
                android:layout_marginLeft="11dp"
                android:src="@drawable/guide3" />

            <ImageView
                android:scaleType="fitXY"
                android:layout_width="353dp"
                android:layout_height="389dp"
                android:layout_marginLeft="11dp"
                android:src="@drawable/guide4" />
        </LinearLayout>
    </ScrollView>

    <TextView
        android:id="@+id/btn_photo"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_gravity="bottom"
        android:background="@color/main_color"
        android:gravity="center"
        android:text="去拍照"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:textStyle="bold" />
</com.qmuiteam.qmui.widget.QMUIWindowInsetLayout>
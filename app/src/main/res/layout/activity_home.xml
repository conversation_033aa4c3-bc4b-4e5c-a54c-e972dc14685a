<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/pager"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintDimensionRatio="1242:927"
        android:layout_width="match_parent"
        android:layout_height="0dp">

        <ImageView
            android:contentDescription="@null"
            android:src="@drawable/bg_home"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

        <FrameLayout
            android:id="@+id/home_native_ad"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    </FrameLayout>

    <RelativeLayout
        app:layout_constraintTop_toBottomOf="@id/pager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:id="@+id/btn_album"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="15dp"
        android:layout_marginRight="14dp"
        android:background="#F7797D"
        android:padding="10dp">

        <ImageView
            android:id="@+id/iv_album"
            android:layout_width="68dp"
            android:layout_height="48dp"
            android:layout_centerVertical="true"
            android:src="@mipmap/ic_home_album" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp"
            android:layout_toRightOf="@+id/iv_album"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="相册"
                android:textColor="#ffffff"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="147dp"
                android:layout_height="29dp"
                android:text="证件照本地相册"
                android:textColor="#ffffffff"
                android:textSize="18sp" />
        </LinearLayout>
    </RelativeLayout>

    <RelativeLayout
        app:layout_constraintTop_toBottomOf="@id/btn_album"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:id="@+id/btn_camera"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="26dp"
        android:layout_marginRight="14dp"
        android:background="#F7797D"
        android:padding="10dp">

        <ImageView
            android:id="@+id/iv_camera"
            android:layout_width="68dp"
            android:layout_height="48dp"
            android:layout_centerVertical="true"
            android:src="@mipmap/ic_home_camera" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp"
            android:layout_toRightOf="@+id/iv_camera"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="相机"
                android:textColor="#ffffff"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="147dp"
                android:layout_height="29dp"
                android:text="一键生成证件照"
                android:textColor="#ffffffff"
                android:textSize="18sp" />
        </LinearLayout>
    </RelativeLayout>

    <FrameLayout
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/home_banner_ad"
        android:layout_width="match_parent"
        android:layout_gravity="bottom"
        android:layout_height="wrap_content"/>
</androidx.constraintlayout.widget.ConstraintLayout>
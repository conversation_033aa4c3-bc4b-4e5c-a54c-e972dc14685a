<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.widget.QMUIWindowInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.qmuiteam.qmui.layout.QMUIFrameLayout
        android:id="@+id/news_new_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/qmui_topbar_height"
        android:fitsSystemWindows="true" />

    <FrameLayout
        android:id="@+id/news_new_ad_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom" />

    <com.qmuiteam.qmui.widget.QMUITopBarLayout
        android:id="@+id/news_new_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_primary_color"
        android:fitsSystemWindows="true" />

</com.qmuiteam.qmui.widget.QMUIWindowInsetLayout>
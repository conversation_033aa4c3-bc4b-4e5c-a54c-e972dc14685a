<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff">

    <com.qmuiteam.qmui.widget.QMUITopBar
        android:id="@+id/top_bar"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        app:qmui_topbar_left_back_drawable_id="@mipmap/ic_back"
        app:qmui_topbar_title_color="#333333" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/bottom_panel"
        android:layout_below="@+id/top_bar"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:background="#f2f2f2">

        <ImageView
            android:id="@+id/cropImageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center" />

    </FrameLayout>


    <LinearLayout
        android:id="@+id/bottom_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#f2f2f2" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal"
            android:weightSum="3">

            <LinearLayout
                android:id="@+id/ll_white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">


                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <View
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/bg_white_dot" />

                    <ImageView
                        android:id="@+id/iv_white"
                        android:layout_width="22dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_select_dark" />
                </FrameLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_blue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">


                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <View
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/bg_blue_dot" />

                    <ImageView
                        android:id="@+id/iv_blue"
                        android:layout_width="22dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_select_light"
                        android:visibility="invisible" />
                </FrameLayout>


            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_red"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">


                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <View
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/bg_red_dot" />

                    <ImageView
                        android:id="@+id/iv_red"
                        android:layout_width="22dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_select_light"
                        android:visibility="invisible" />
                </FrameLayout>

            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/btn_next"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="45dp"
            android:layout_marginTop="30dp"
            android:layout_marginRight="45dp"
            android:layout_marginBottom="30dp"
            android:background="@color/main_color"
            android:gravity="center"
            android:text="下一步"
            android:textColor="#fff"
            android:textSize="18sp" />
    </LinearLayout>

</RelativeLayout>


<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#f2f2f2">


    <LinearLayout
            android:id="@+id/fake_dialog"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="-40dp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:text="@string/welcome_app"
                android:textColor="#666666"
                android:textSize="24dp"
                android:textStyle="bold" />


        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout
                android:layout_width="320dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                app:qmui_backgroundColor="#ffffff"
                app:qmui_radius="24dp">

            <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="30dp"
                    android:text="@string/privacy_policy"
                    android:textColor="#000000"
                    android:textSize="24sp"
                    android:textStyle="bold" />


            <TextView
                    android:id="@+id/tv_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:lineSpacingMultiplier="1.2"
                    android:text="@string/content"
                    android:textSize="18sp" />


            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    android:id="@+id/btn_ok"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="30dp"
                    android:layout_marginRight="12dp"
                    android:text="@string/btn_agree_and_continue"
                    android:textColor="#FFFFFF"
                    android:textSize="18sp"
                    app:qmui_backgroundColor="#5C3BED"
                    app:qmui_borderColor="#5C3BED" />

            <TextView
                    android:id="@+id/btn_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="30dp"
                    android:layout_marginBottom="40dp"
                    android:text="@string/btn_disagree_and_exit"
                    android:textColor="#666666"
                    android:textSize="18sp"
                    android:textStyle="bold" />


        </com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout>

    </LinearLayout>

</FrameLayout>
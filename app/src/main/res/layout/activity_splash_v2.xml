<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                   xmlns:app="http://schemas.android.com/apk/res-auto"
                                                   xmlns:tools="http://schemas.android.com/tools"
                                                   android:layout_width="match_parent"
                                                   android:layout_height="match_parent"
                                                   android:background="@android:color/white">

    <!-- 顶部品牌标志区域，垂直居中 -->
    <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/splash_branding_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.45">

        <ImageView
                android:id="@+id/splash_icon"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:contentDescription="@null"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@mipmap/ic_launcher_zjz_2" />

        <TextView
                android:id="@+id/splash_app_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:textColor="#333333"
                android:textSize="28sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@id/splash_icon"
                app:layout_constraintStart_toStartOf="@id/splash_icon"
                app:layout_constraintTop_toBottomOf="@id/splash_icon"
                tools:text="@string/app_name" />

        <TextView
                android:padding="16dp"
                android:id="@+id/splash_slogan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text="@string/app_slogn"
                android:textColor="#666666"
                android:textSize="16sp"
                app:layout_constraintEnd_toEndOf="@id/splash_app_name"
                app:layout_constraintStart_toStartOf="@id/splash_app_name"
                app:layout_constraintTop_toBottomOf="@id/splash_app_name" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 底部版权或公司信息 -->
    <TextView
            android:id="@+id/splash_copyright"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:gravity="center"
            android:text="©2025 Shanghai Lanxi Information Technology Co., Ltd. "
            android:textColor="#999999"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    <!-- 用于加载或动画的容器 -->
    <FrameLayout
            android:id="@+id/splash_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
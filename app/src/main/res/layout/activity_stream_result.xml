<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.qmuiteam.qmui.widget.QMUITopBar
        android:id="@+id/top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 图片显示区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:orientation="vertical">

        <!-- 圆形图片容器 -->
        <androidx.cardview.widget.CardView
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_gravity="center"
            app:cardCornerRadius="100dp"
            app:cardElevation="8dp"
            app:cardUseCompatPadding="true">

            <ImageView
                android:id="@+id/iv_result"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                tools:src="@drawable/ic_launcher_background" />

        </androidx.cardview.widget.CardView>

        <!-- 识别状态指示器 -->
        <LinearLayout
            android:id="@+id/ll_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ProgressBar
                android:id="@+id/progress_bar"
                style="?android:attr/progressBarStyleSmall"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="8dp"
                android:indeterminateTint="@color/main_color" />

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/recognizing"
                android:textColor="@color/main_color"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="#E0E0E0" />

    <!-- 结果显示区域 -->
    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Markdown渲染的TextView -->
            <TextView
                android:id="@+id/tv_result"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.3"
                android:textIsSelectable="true"
                android:textSize="16sp"
                android:textColor="#333333"
                tools:text="识别结果将在这里显示..." />

        </LinearLayout>

    </ScrollView>

    <!-- 底部操作栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#F5F5F5"
        android:orientation="horizontal"
        android:padding="8dp">

        <TextView
            android:id="@+id/btn_copy"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/btn_primary_selector"
            android:gravity="center"
            android:text="@string/copy"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/btn_share"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@drawable/btn_secondary_selector"
            android:gravity="center"
            android:text="@string/share"
            android:textColor="@color/main_color"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>

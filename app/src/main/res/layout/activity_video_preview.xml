<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <VideoView
            android:id="@+id/video"
            android:layout_width="match_parent"
            android:layout_height="200dp"/>

        <com.dicernszh.botany.MessageView
            android:id="@+id/actualResolution"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <com.dicernszh.botany.MessageView
            android:id="@+id/isSnapshot"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <com.dicernszh.botany.MessageView
            android:id="@+id/rotation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <com.dicernszh.botany.MessageView
            android:id="@+id/audio"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <com.dicernszh.botany.MessageView
            android:id="@+id/audioBitRate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <com.dicernszh.botany.MessageView
            android:id="@+id/videoCodec"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <com.dicernszh.botany.MessageView
            android:id="@+id/videoBitRate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <com.dicernszh.botany.MessageView
            android:id="@+id/videoFrameRate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

    </LinearLayout>
</ScrollView>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingLeft="15dp"
    android:paddingRight="15dp"
    android:paddingBottom="15dp">

    <RelativeLayout
        android:id="@+id/close_indicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center_horizontal"
            android:background="#D8D8D8"
            android:minWidth="50dp"
            android:minHeight="6dp"
            app:qmui_backgroundColor="#D8D8D8"
            app:qmui_borderColor="#D8D8D8"
            app:qmui_isRadiusAdjustBounds="true" />


    </RelativeLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@+id/iv_holder"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="拍摄须知"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="请选择光线充足明亮的，与衣服颜色不同的纯色背景前，正对镜头露出双耳，尽量让他人协助使用后置摄像头拍摄。"
                android:textColor="#666666" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_holder"
            android:layout_width="93dp"
            android:layout_height="112dp"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="22dp"
            android:src="@drawable/img_popup_picture1" />
    </RelativeLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="错误示范"
        android:textColor="@color/black"
        android:textSize="14sp"
        android:textStyle="bold" />


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        android:weightSum="3">

        <ImageView
            android:layout_width="93dp"
            android:layout_height="112dp"
            android:layout_weight="1"
            android:src="@drawable/img_popup_2" />

        <ImageView
            android:layout_width="93dp"
            android:layout_height="112dp"
            android:layout_centerHorizontal="true"
            android:layout_weight="1"
            android:src="@drawable/img_popup_3" />

        <ImageView
            android:layout_width="93dp"
            android:layout_height="112dp"
            android:layout_alignParentRight="true"
            android:layout_weight="1"
            android:src="@drawable/img_popup_4" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp">

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_photo"
            android:layout_width="160dp"
            android:layout_height="48dp"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:text="相册上传"
            android:textColor="@color/main_color"
            android:textSize="16sp"
            app:qmui_borderColor="@color/main_color"
            app:qmui_radius="4dp" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_camera"
            android:layout_width="160dp"
            android:layout_height="48dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:text="直接拍摄"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:qmui_backgroundColor="#6053FE"
            app:qmui_radius="4dp" />

    </RelativeLayout>


</LinearLayout>
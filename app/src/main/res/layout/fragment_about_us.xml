<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.widget.QMUIWindowInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/qmui_config_color_white">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/qmui_topbar_height"
        android:fillViewport="true"
        android:fitsSystemWindows="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingTop="140dp"
            android:paddingBottom="25dp">

            <ImageView
                android:id="@+id/app_icon"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:contentDescription="@null"
                android:src="@drawable/ic_logo" />

            <TextView
                android:id="@+id/version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:textColor="?attr/qmui_config_color_gray_3"
                android:textSize="16sp" />

            <com.qmuiteam.qmui.widget.grouplist.QMUIGroupListView
                android:id="@+id/about_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="35dp" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/qq_msg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:gravity="center_horizontal"
                android:text="@string/qq"
                android:textColor="?attr/qmui_config_color_gray_7" />

        </LinearLayout>

    </ScrollView>


    <com.qmuiteam.qmui.widget.QMUITopBarLayout
        android:id="@+id/about_us_topbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:qmui_topbar_left_back_drawable_id="@mipmap/ic_back"
        app:qmui_topbar_title_color="#000000" />

</com.qmuiteam.qmui.widget.QMUIWindowInsetLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Camera -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#000000">

        <com.otaliastudios.cameraview.CameraView
            android:id="@+id/camera"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:keepScreenOn="true"
            app:cameraAudio="on"
            app:cameraAutoFocusMarker="@string/cameraview_default_autofocus_marker"
            app:cameraEngine="camera2"
            app:cameraExperimental="true"
            app:cameraFacing="back"
            app:cameraFlash="auto"
            app:cameraGestureLongTap="none"
            app:cameraGesturePinch="zoom"
            app:cameraGestureScrollHorizontal="filterControl1"
            app:cameraGestureScrollVertical="exposureCorrection"
            app:cameraGestureTap="autoFocus"
            app:cameraGrid="off"
            app:cameraMode="picture"
            app:cameraPlaySounds="true"
            app:cameraPreview="glSurface"
            app:layout_constraintBottom_toTopOf="@+id/btns"
            app:layout_constraintTop_toTopOf="parent" />

        <RelativeLayout
            android:layout_marginBottom="49dp"
            android:id="@+id/btns"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
            android:padding="16dp"
            android:weightSum="4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_history"
                    android:layout_width="36dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:src="@drawable/icon_histoty" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="历史"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>


            <ImageView
                android:id="@+id/capturePictureSnapshot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:background="@drawable/bg_btn_camera" />


            <LinearLayout
                    android:visibility="invisible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:orientation="vertical">

                <ImageView
                        android:visibility="invisible"
                        android:id="@+id/chooseImage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        />

                <TextView
                        android:visibility="invisible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>

        </RelativeLayout>


        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="centerInside"
            android:scaleX="1.4"
            android:scaleY="1.4"
            android:src="@drawable/ic_et"
            app:layout_constraintBottom_toBottomOf="@+id/camera"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@+id/camera"
            app:layout_constraintTop_toTopOf="@+id/camera" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:text="请将手机保持水平状态"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>

<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.widget.QMUIWindowInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.qmuiteam.qmui.widget.QMUITopBarLayout
            android:id="@+id/fav_topbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="技巧"
                    android:textSize="20sp" />
            </FrameLayout>

        </com.qmuiteam.qmui.widget.QMUITopBarLayout>


        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#f2f2f2"
            android:fitsSystemWindows="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout
                    android:id="@+id/fav_take_photo_skills"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="15dp"
                    android:layout_marginRight="15dp"
                    android:layout_marginBottom="15dp"
                    android:orientation="vertical"
                    app:qmui_backgroundColor="#ffffff"
                    app:qmui_radius="6dp">

                    <ImageView
                        android:id="@+id/fav_1_iv"
                        android:layout_width="345dp"
                        android:layout_height="330dp"
                        android:contentDescription="@null"
                        android:src="@drawable/fav1" />

                    <RelativeLayout
                        android:visibility="gone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:layout_marginBottom="15dp"
                        android:paddingLeft="15dp"
                        android:paddingRight="15dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:text="证件照拍照技巧"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:clickable="false"
                            android:minWidth="52dp"
                            android:minHeight="26dp"
                            android:text="查看"
                            android:textColor="#ffffff"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:qmui_backgroundColor="#2D63E9"
                            app:qmui_radius="4dp" />
                    </RelativeLayout>

                </com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout>


                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout
                    android:id="@+id/fav_visa_photos_reference"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:layout_marginLeft="15dp"
                    android:layout_marginEnd="15dp"
                    android:layout_marginRight="15dp"
                    android:layout_marginBottom="15dp"
                    android:orientation="vertical"
                    app:qmui_backgroundColor="#ffffff"
                    app:qmui_radius="6dp">

                    <ImageView
                        android:id="@+id/fav_2_iv"
                        android:layout_width="345dp"
                        android:layout_height="330dp"
                        android:contentDescription="@null"
                        android:src="@drawable/fav2" />

                    <RelativeLayout
                        android:visibility="gone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:layout_marginBottom="15dp"
                        android:paddingLeft="15dp"
                        android:paddingRight="15dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:text="常用签证照规格"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:clickable="false"
                            android:minWidth="52dp"
                            android:minHeight="26dp"
                            android:text="查看"
                            android:textColor="#ffffff"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:qmui_backgroundColor="#2D63E9"
                            app:qmui_radius="4dp" />
                    </RelativeLayout>

                </com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout>


                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout
                    android:id="@+id/fav_id_photos_reference"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:layout_marginLeft="15dp"
                    android:layout_marginEnd="15dp"
                    android:layout_marginRight="15dp"
                    android:layout_marginBottom="15dp"
                    android:orientation="vertical"
                    app:qmui_backgroundColor="#ffffff"
                    app:qmui_radius="6dp">

                    <ImageView
                        android:id="@+id/fav_3_iv"
                        android:layout_width="345dp"
                        android:layout_height="330dp"
                        android:contentDescription="@null"
                        android:scaleType="fitXY"
                        android:src="@drawable/fav3" />

                    <RelativeLayout
                        android:visibility="gone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:layout_marginBottom="15dp"
                        android:paddingLeft="15dp"
                        android:paddingRight="15dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:text="常用证件照规格"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:clickable="false"
                            android:minWidth="52dp"
                            android:minHeight="26dp"
                            android:text="查看"
                            android:textColor="#ffffff"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:qmui_backgroundColor="#2D63E9"
                            app:qmui_radius="4dp" />
                    </RelativeLayout>

                </com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>


</com.qmuiteam.qmui.widget.QMUIWindowInsetLayout>
<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.widget.QMUIWindowInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="match_parent">

    <com.qmuiteam.qmui.widget.QMUITopBarLayout
        android:id="@+id/feedback_topbar"
        app:qmui_topbar_title_color="#000000"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:qmui_topbar_left_back_drawable_id="@mipmap/ic_back" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/qmui_topbar_height"
        android:fitsSystemWindows="true">

        <EditText
            android:id="@+id/feedback_edit_feedback"
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:layout_margin="16dp"
            android:background="@drawable/edittext_border"
            android:gravity="top"
            android:hint="@string/str_feedback_content"
            android:inputType=""
            android:padding="16dp"
            android:textColor="@color/colorBlack"
            android:textColorHint="#999999"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/feedback_edit_qq"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:layout_margin="16dp"
            android:background="@drawable/edittext_border"
            android:hint="@string/str_feedback_qq"
            android:inputType="number"
            android:maxLines="1"
            android:padding="8dp"
            android:textColor="@color/colorBlack"
            android:textColorHint="#999999"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/feedback_edit_feedback" />

        <Button
            android:id="@+id/feedback_btn_submit"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:layout_marginStart="16dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:background="@drawable/feedback_submit_selector"
            android:text="@string/submit"
            android:textColor="@color/colorWhite"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/feedback_edit_qq" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.qmuiteam.qmui.widget.QMUIWindowInsetLayout>
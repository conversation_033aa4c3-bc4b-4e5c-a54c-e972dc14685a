<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:fitsSystemWindows="true"
              android:background="@color/app_content_bg_color"
              android:orientation="vertical">

    <FrameLayout
            android:minHeight="32dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/home_title"
                android:textSize="16sp"
                android:textStyle="bold" />
    </FrameLayout>

    <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="12dp"
                android:paddingLeft="15dp"
                android:paddingRight="15dp">

            <ImageView
                    android:id="@+id/iv_banner"
                    android:layout_width="345dp"
                    android:layout_height="152dp"
                    android:src="@drawable/banner"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


            <FrameLayout
                    android:id="@+id/ad_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/iv_banner" />

            <!-- 文字叠加在图片上 -->
            <FrameLayout
                    android:id="@+id/iv_scan"
                    android:layout_width="165dp"
                    android:layout_height="100dp"
                    android:layout_marginTop="24dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ad_container">

                <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/img_11" />

                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="top"
                        android:paddingTop="8dp">

                    <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/take_photo_title"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:gravity="left"
                            android:layout_marginLeft="12dp"
                            android:layout_marginBottom="4dp" />

                    <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/take_photo_desc"
                            android:textColor="#999999"
                            android:textSize="12sp"
                            android:gravity="left"
                            android:layout_marginLeft="12dp"
                            android:layout_marginTop="4dp" />
                </LinearLayout>
            </FrameLayout>

            <!-- 第二个图片也使用同样的方式 -->
            <FrameLayout
                    android:id="@+id/iv_local"
                    android:layout_width="165dp"
                    android:layout_height="100dp"
                    android:layout_marginTop="24dp"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ad_container">

                <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/img_12" />

                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="top"
                        android:paddingTop="8dp">

                    <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/gallery_photo_title"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:gravity="left"
                            android:layout_marginLeft="12dp"
                            android:layout_marginBottom="4dp" />

                    <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/gallery_photo_desc"
                            android:textColor="#999999"
                            android:textSize="12sp"
                            android:gravity="left"
                            android:layout_marginLeft="12dp"
                            android:layout_marginTop="4dp" />
                </LinearLayout>
            </FrameLayout>

            <TextView
                    android:id="@+id/tv_section"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:gravity="left"
                    android:text="@string/precise_recognition"
                    android:textColor="#FF373126"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/iv_local" />


            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintTop_toBottomOf="@+id/tv_section">

                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:orientation="horizontal">

                    <!-- 小卡片也使用同样方式 -->
                    <FrameLayout
                            android:id="@+id/iv_cell1"
                            android:layout_width="162dp"
                            android:layout_height="79dp">

                        <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/img_21" />

                        <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="top"
                                android:paddingTop="8dp">

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/plant_recognition"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginBottom="2dp" />

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/plant_recognition_desc"
                                    android:textColor="#999999"
                                    android:textSize="11sp"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </FrameLayout>

                    <FrameLayout
                            android:id="@+id/iv_cell2"
                            android:layout_width="162dp"
                            android:layout_height="79dp"
                            android:layout_marginLeft="21dp">

                        <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/img_22" />

                        <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="top"
                                android:paddingTop="8dp">

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/animal_recognition"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginBottom="2dp" />

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/animal_recognition_desc"
                                    android:textColor="#999999"
                                    android:textSize="11sp"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </FrameLayout>
                </LinearLayout>


                <FrameLayout
                        android:id="@+id/ad_feed1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:orientation="horizontal">

                    <FrameLayout
                            android:id="@+id/iv_cell3"
                            android:layout_width="162dp"
                            android:layout_height="79dp">

                        <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/img_23" />

                        <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="top"
                                android:paddingTop="8dp">

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/flower_recognition"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginBottom="2dp" />

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/flower_recognition_desc"
                                    android:textColor="#999999"
                                    android:textSize="11sp"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </FrameLayout>

                    <FrameLayout
                            android:id="@+id/iv_cell4"
                            android:layout_width="162dp"
                            android:layout_height="79dp"
                            android:layout_marginLeft="21dp">

                        <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/img_24" />

                        <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="top"
                                android:paddingTop="8dp">

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/fruit_veg_recognition"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginBottom="2dp" />

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/fruit_veg_recognition_desc"
                                    android:textColor="#999999"
                                    android:textSize="11sp"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </FrameLayout>
                </LinearLayout>

                <FrameLayout
                        android:id="@+id/ad_feed2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:orientation="horizontal">

                    <FrameLayout
                            android:id="@+id/iv_cell5"
                            android:layout_width="162dp"
                            android:layout_height="79dp">

                        <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/img_25" />

                        <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="top"
                                android:paddingTop="8dp">

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/logo_recognition"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginBottom="2dp" />

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/logo_recognition_desc"
                                    android:textColor="#999999"
                                    android:textSize="11sp"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </FrameLayout>

                    <FrameLayout
                            android:id="@+id/iv_cell6"
                            android:layout_width="162dp"
                            android:layout_height="79dp"
                            android:layout_marginLeft="21dp">

                        <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/img_26" />

                        <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="top"
                                android:paddingTop="8dp">

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/dish_recognition"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginBottom="2dp" />

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/dish_recognition_desc"
                                    android:textColor="#999999"
                                    android:textSize="11sp"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </FrameLayout>
                </LinearLayout>

                <FrameLayout
                        android:id="@+id/ad_feed3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:orientation="horizontal">

                    <FrameLayout
                            android:id="@+id/iv_cell7"
                            android:layout_width="162dp"
                            android:layout_height="79dp">

                        <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/img_27" />

                        <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="top"
                                android:paddingTop="8dp">

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/landmark_recognition"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginBottom="2dp" />

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/landmark_recognition_desc"
                                    android:textColor="#999999"
                                    android:textSize="11sp"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </FrameLayout>

                    <FrameLayout
                            android:id="@+id/iv_cell8"
                            android:layout_width="162dp"
                            android:layout_height="79dp"
                            android:layout_marginLeft="21dp">

                        <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/img_28" />

                        <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="top"
                                android:paddingTop="8dp">

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/currency_recognition"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginBottom="2dp" />

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/currency_recognition_desc"
                                    android:textColor="#999999"
                                    android:textSize="11sp"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </FrameLayout>
                </LinearLayout>


                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginBottom="40dp"
                        android:orientation="horizontal">

                    <FrameLayout
                            android:id="@+id/iv_cell9"
                            android:layout_width="162dp"
                            android:layout_height="79dp">

                        <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/img_29" />

                        <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="top"
                                android:paddingTop="8dp">

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/product_recognition"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginBottom="2dp" />

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/product_recognition_desc"
                                    android:textColor="#999999"
                                    android:textSize="11sp"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </FrameLayout>

                    <FrameLayout
                            android:id="@+id/iv_cell10"
                            android:layout_width="162dp"
                            android:layout_height="79dp"
                            android:layout_marginLeft="21dp">

                        <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/img_210" />

                        <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="top"
                                android:paddingTop="8dp">

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/text_recognition"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginBottom="2dp" />

                            <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/text_recognition_desc"
                                    android:textColor="#999999"
                                    android:textSize="11sp"
                                    android:gravity="left"
                                    android:layout_marginLeft="12dp"
                                    android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </FrameLayout>
                </LinearLayout>
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>


    </ScrollView>


</LinearLayout>
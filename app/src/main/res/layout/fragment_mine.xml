<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
             xmlns:tools="http://schemas.android.com/tools"
             android:layout_width="match_parent"
             android:layout_height="match_parent"
   >
    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical" tools:ignore="UselessParent">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="183dp"
            android:background="@drawable/bg_mine"
            android:fitsSystemWindows="true">

            <ImageView
                    android:visibility="invisible"
                android:id="@+id/avatar"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:src="@drawable/portrait"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView

                    android:id="@+id/login"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="注册/登陆"
                android:textColor="#FFFFFF"
                android:textSize="18sp"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/avatar" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="识植物"
                android:visibility="invisible"
                android:textColor="#FFFFFFFF"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/login" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#EEEEEE"
            android:fitsSystemWindows="true">

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:qmui_backgroundColor="#EEEEEE">

                <com.qmuiteam.qmui.widget.grouplist.QMUIGroupListView
                    android:id="@+id/mine_group_list_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:qmui_backgroundColor="#EEEEEE" />

            </com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout>

        </androidx.core.widget.NestedScrollView>
    </LinearLayout>



</FrameLayout>
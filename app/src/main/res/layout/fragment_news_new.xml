<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.widget.QMUIWindowInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.qmuiteam.qmui.layout.QMUILinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/qmui_topbar_height"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/news_tab_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tabIndicatorColor="@color/tab_select_color"
            app:tabMinWidth="30dp"
            app:tabMode="scrollable"
            app:tabTextColor="@color/tab_select_color" />

        <com.qmuiteam.qmui.widget.QMUIViewPager
            android:id="@+id/news_view_page"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.qmuiteam.qmui.layout.QMUILinearLayout>

    <com.qmuiteam.qmui.widget.QMUITopBarLayout
        android:id="@+id/news_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_primary_color"
        android:fitsSystemWindows="true" />

</com.qmuiteam.qmui.widget.QMUIWindowInsetLayout>
<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.widget.QMUIWindowInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.qmuiteam.qmui.widget.QMUITopBarLayout
            android:id="@+id/fav_topbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="首页"
                    android:textSize="20sp" />
            </FrameLayout>

        </com.qmuiteam.qmui.widget.QMUITopBarLayout>


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/banner"
                    android:layout_width="345dp"
                    android:layout_height="132dp"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="16dp"
                    android:contentDescription="@null"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_banner" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:weightSum="2">

                    <com.qmuiteam.qmui.layout.QMUIRelativeLayout
                        android:id="@+id/cell1"
                        android:layout_width="165dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="15dp"
                        android:layout_marginRight="15dp"
                        android:layout_weight="1"
                        android:background="#FFE4B8"
                        android:minHeight="76dp"
                        app:qmui_backgroundColor="#6053FE"
                        app:qmui_radius="4dp">

                        <ImageView
                            android:id="@+id/iv_cell1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="10dp"
                            android:src="@drawable/image_cell" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="16dp"
                            android:layout_toRightOf="@+id/iv_cell1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="本地照片编辑"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="支持各种图片尺寸" />

                        </LinearLayout>


                    </com.qmuiteam.qmui.layout.QMUIRelativeLayout>

                    <com.qmuiteam.qmui.layout.QMUIRelativeLayout
                        android:id="@+id/cell2"
                        android:layout_width="165dp"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="15dp"
                        android:layout_weight="1"
                        android:background="#ABE3FF"
                        android:minHeight="76dp"
                        app:qmui_backgroundColor="#6053FE"
                        app:qmui_radius="4dp">

                        <ImageView
                            android:id="@+id/iv_cell2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="10dp"
                            android:src="@drawable/camera_cell" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="16dp"
                            android:layout_toRightOf="@+id/iv_cell2"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="常用寸照"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="各种寸照随心拍" />

                        </LinearLayout>


                    </com.qmuiteam.qmui.layout.QMUIRelativeLayout>

                </LinearLayout>


                <!--                <androidx.constraintlayout.widget.ConstraintLayout-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="match_parent"-->
                <!--                    android:fitsSystemWindows="true"-->
                <!--                    android:orientation="vertical"-->
                <!--                    app:layout_constraintBottom_toBottomOf="parent"-->
                <!--                    app:layout_constraintEnd_toEndOf="parent"-->
                <!--                    app:layout_constraintStart_toStartOf="parent"-->
                <!--                    app:layout_constraintTop_toTopOf="parent">-->

                <!--                    <RelativeLayout-->
                <!--                        android:id="@+id/btn_album"-->
                <!--                        android:layout_width="match_parent"-->
                <!--                        android:layout_height="155dp"-->
                <!--                        android:layout_marginLeft="14dp"-->
                <!--                        android:layout_marginTop="15dp"-->
                <!--                        android:layout_marginRight="14dp"-->
                <!--                        android:background="#F7797D"-->
                <!--                        android:visibility="gone"-->
                <!--                        app:layout_constraintEnd_toEndOf="parent"-->
                <!--                        app:layout_constraintStart_toStartOf="parent"-->
                <!--                        app:layout_constraintTop_toTopOf="parent">-->

                <!--                        <ImageView-->
                <!--                            android:id="@+id/iv_album"-->
                <!--                            android:layout_width="97dp"-->
                <!--                            android:layout_height="81dp"-->
                <!--                            android:layout_centerVertical="true"-->
                <!--                            android:layout_marginLeft="29dp"-->
                <!--                            android:scaleType="centerInside"-->
                <!--                            android:src="@mipmap/ic_home_album" />-->

                <!--                        <LinearLayout-->
                <!--                            android:layout_width="wrap_content"-->
                <!--                            android:layout_height="wrap_content"-->
                <!--                            android:layout_centerVertical="true"-->
                <!--                            android:layout_marginLeft="20dp"-->
                <!--                            android:layout_toRightOf="@+id/iv_album"-->
                <!--                            android:orientation="vertical"-->
                <!--                            android:paddingLeft="15dp">-->

                <!--                            <TextView-->
                <!--                                android:layout_width="wrap_content"-->
                <!--                                android:layout_height="wrap_content"-->
                <!--                                android:text="相册"-->
                <!--                                android:textColor="#ffffff"-->
                <!--                                android:textSize="24sp"-->
                <!--                                android:textStyle="bold" />-->

                <!--                            <TextView-->
                <!--                                android:layout_width="147dp"-->
                <!--                                android:layout_height="29dp"-->
                <!--                                android:text="证件照本地相册"-->
                <!--                                android:textColor="#ffffffff"-->
                <!--                                android:textSize="18sp" />-->
                <!--                        </LinearLayout>-->
                <!--                    </RelativeLayout>-->

                <!--                    <RelativeLayout-->
                <!--                        android:id="@+id/btn_camera"-->
                <!--                        android:layout_width="match_parent"-->
                <!--                        android:layout_height="155dp"-->
                <!--                        android:layout_marginLeft="14dp"-->
                <!--                        android:layout_marginTop="26dp"-->
                <!--                        android:layout_marginRight="14dp"-->
                <!--                        android:background="#FF5F85"-->
                <!--                        android:visibility="gone"-->
                <!--                        app:layout_constraintEnd_toEndOf="parent"-->
                <!--                        app:layout_constraintStart_toStartOf="parent"-->
                <!--                        app:layout_constraintTop_toBottomOf="@id/btn_album">-->

                <!--                        <ImageView-->
                <!--                            android:id="@+id/iv_camera"-->
                <!--                            android:layout_width="97dp"-->
                <!--                            android:layout_height="81dp"-->
                <!--                            android:layout_centerVertical="true"-->
                <!--                            android:layout_marginLeft="29dp"-->
                <!--                            android:src="@mipmap/ic_home_camera" />-->

                <!--                        <LinearLayout-->
                <!--                            android:layout_width="wrap_content"-->
                <!--                            android:layout_height="wrap_content"-->
                <!--                            android:layout_centerVertical="true"-->
                <!--                            android:layout_marginLeft="20dp"-->
                <!--                            android:layout_toRightOf="@+id/iv_camera"-->
                <!--                            android:orientation="vertical">-->

                <!--                            <TextView-->
                <!--                                android:layout_width="wrap_content"-->
                <!--                                android:layout_height="wrap_content"-->
                <!--                                android:text="相机"-->
                <!--                                android:textColor="#ffffff"-->
                <!--                                android:textSize="24sp"-->
                <!--                                android:textStyle="bold" />-->

                <!--                            <TextView-->
                <!--                                android:layout_width="147dp"-->
                <!--                                android:layout_height="29dp"-->
                <!--                                android:text="一键生成证件照"-->
                <!--                                android:textColor="#ffffffff"-->
                <!--                                android:textSize="18sp" />-->
                <!--                        </LinearLayout>-->
                <!--                    </RelativeLayout>-->

                <!--                </androidx.constraintlayout.widget.ConstraintLayout>-->

                <!--                <FrameLayout-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="wrap_content">-->

                <!--                    <FrameLayout-->
                <!--                        android:id="@+id/home_native_ad"-->
                <!--                        android:layout_width="wrap_content"-->
                <!--                        android:layout_height="wrap_content"-->
                <!--                        android:layout_gravity="center" />-->

                <!--                </FrameLayout>-->

                <RelativeLayout
                    android:id="@+id/section_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="常用规格"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_toLeftOf="@+id/iv_more"
                        android:gravity="center"
                        android:text="更多"
                        android:textColor="#999999" />

                    <ImageView
                        android:id="@+id/iv_more"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/ic_more" />
                </RelativeLayout>
            </LinearLayout>

        </ScrollView>


    </LinearLayout>


</com.qmuiteam.qmui.widget.QMUIWindowInsetLayout>
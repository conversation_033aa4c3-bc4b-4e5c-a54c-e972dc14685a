<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.widget.QMUIWindowInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.qmuiteam.qmui.widget.QMUITopBarLayout
        android:id="@+id/pp_topbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:qmui_topbar_left_back_drawable_id="@mipmap/ic_back"
        app:qmui_topbar_title_color="#000000" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <com.qmuiteam.qmui.widget.webview.QMUIWebView
            android:id="@+id/pp_web"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="?attr/qmui_topbar_height" />

    </FrameLayout>

</com.qmuiteam.qmui.widget.QMUIWindowInsetLayout>
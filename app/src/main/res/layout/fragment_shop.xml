<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.widget.QMUIWindowInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="?attr/qmui_topbar_height"
        android:fitsSystemWindows="true" />


    <com.qmuiteam.qmui.widget.QMUITopBarLayout
        android:id="@+id/mine_topbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:qmui_topbar_left_back_drawable_id="@mipmap/ic_back"
        app:qmui_topbar_title_color="#000000">

        <FrameLayout
            android:id="@+id/inner_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="冲印"
                android:textSize="20sp" />
        </FrameLayout>

    </com.qmuiteam.qmui.widget.QMUITopBarLayout>


</com.qmuiteam.qmui.widget.QMUIWindowInsetLayout>
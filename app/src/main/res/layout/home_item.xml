<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="16dp"
    android:layout_marginTop="8dp"
    android:layout_marginRight="16dp"
    android:layout_marginBottom="8dp"
    android:background="@drawable/bg_item"
    android:gravity="center_vertical"
    android:padding="16dp">

    <ImageView
        android:id="@+id/iv_leading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:src="@drawable/item_img_leading" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="14px"
        android:layout_toRightOf="@+id/iv_leading"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="一寸"
            android:textStyle="bold"
            android:textColor="#000000"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:text="295*413px|25*35mm"
            android:textColor="#999999"
            android:textSize="12sp" />
    </LinearLayout>


    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:src="@drawable/ic_more" />

</RelativeLayout>


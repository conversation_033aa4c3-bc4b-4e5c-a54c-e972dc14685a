<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <ImageView
        android:scaleType="centerCrop"
        android:id="@+id/iv_cover"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_marginTop="16dp"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#D8D8D8"
        android:layout_marginLeft="16dp"
        app:layout_constraintTop_toBottomOf="@+id/iv_cover" />

    <TextView
        android:textStyle="bold"
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:textSize="14sp"
        app:layout_constraintLeft_toRightOf="@+id/iv_cover"
        app:layout_constraintTop_toTopOf="@+id/iv_cover" />

    <TextView
        android:id="@+id/tv_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
        app:layout_constraintLeft_toRightOf="@+id/iv_cover" />


</androidx.constraintlayout.widget.ConstraintLayout>
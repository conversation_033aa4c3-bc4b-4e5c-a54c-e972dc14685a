<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/news_item_1"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?selectableItemBackground">

    <ImageView
        android:id="@+id/news_item_iv"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="8dp"
        android:contentDescription="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/news_item_title"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/news_item_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginLeft="16dp"
        android:layout_marginEnd="114dp"
        android:layout_marginRight="114dp"
        android:ellipsize="end"
        android:lineSpacingExtra="4dp"
        android:maxLines="2"
        android:paddingTop="8dp"
        android:textColor="@color/news_title_color"
        android:textSize="16sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="中甲最新积分榜：黄海提前冲超成功，恒丰亚泰永昌争夺剩余名额" />

    <View
        android:id="@+id/item_news_one_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="8dp"
        android:background="@color/news_divider_color"
        android:paddingBottom="1dp"
        app:layout_constraintBottom_toTopOf="@id/item_news_one_line_native_ad_wrapper"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/news_item_iv" />

    <FrameLayout
        android:id="@+id/item_news_one_line_native_ad_wrapper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/item_news_one_line">

        <FrameLayout
            android:id="@+id/item_news_one_line_native_ad"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
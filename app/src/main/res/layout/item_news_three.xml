<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/news_item_3"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?selectableItemBackground">

    <TextView
        android:id="@+id/news_item_title_th"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginLeft="16dp"
        android:paddingTop="8dp"
        android:layout_marginEnd="16dp"
        android:ellipsize="end"
        android:lineSpacingExtra="4dp"
        android:maxLines="2"
        android:textColor="@color/news_title_color"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="中甲最新积分榜：黄海提前冲超成功，恒丰亚泰永昌争夺剩余名额" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/news_item_ivs"
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:layout_marginStart="16dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:layout_marginRight="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/news_item_title_th">

        <ImageView
            android:id="@+id/news_item_title_th_iv1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="1dp"
            android:layout_marginRight="1dp"
            android:contentDescription="@null"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/news_item_title_th_iv2" />

        <ImageView
            android:id="@+id/news_item_title_th_iv2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="1dp"
            android:layout_marginRight="1dp"
            android:contentDescription="@null"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@id/news_item_title_th_iv1"
            app:layout_constraintRight_toLeftOf="@id/news_item_title_th_iv3" />

        <ImageView
            android:id="@+id/news_item_title_th_iv3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="1dp"
            android:layout_marginRight="1dp"
            android:contentDescription="@null"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@id/news_item_title_th_iv2"
            app:layout_constraintRight_toRightOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/item_news_three_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="8dp"
        android:background="@color/news_divider_color"
        app:layout_constraintBottom_toTopOf="@id/item_news_three_line_native_ad_wrapper"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/news_item_ivs" />

    <FrameLayout
        android:id="@+id/item_news_three_line_native_ad_wrapper"
        android:layout_width="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/item_news_three_line"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="wrap_content">

        <FrameLayout
            android:id="@+id/item_news_three_line_native_ad"
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
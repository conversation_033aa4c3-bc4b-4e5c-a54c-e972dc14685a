<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical">

    <ImageView
        android:id="@+id/iv_shop"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:layout_margin="16dp"
        android:src="@drawable/ic_shop" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/iv_shop"
        android:text="柯达影像"
        android:textColor="#666"
        android:textSize="18sp" />


    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:clickable="false"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="16dp"
        android:minWidth="44dp"
        android:minHeight="20dp"
        android:text="进店"
        android:textColor="@color/white"
        app:qmui_borderColor="@color/main_color"
        app:qmui_isRadiusAdjustBounds="true"
        app:qmui_backgroundColor="@color/main_color"
       />


    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:background="#DDDDDD" />


</RelativeLayout>
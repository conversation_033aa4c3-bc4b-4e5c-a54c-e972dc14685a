<?xml version="1.0" encoding="utf-8"?>
<merge
    xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:orientation="horizontal"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp"
        android:gravity="center_vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">

        <TextView
            style="@style/TextAppearance.AppCompat.Subhead"
            android:id="@+id/title"
            android:textStyle="bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <FrameLayout
            android:id="@+id/content"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"/>

    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#44AAAAAA" />


</merge>

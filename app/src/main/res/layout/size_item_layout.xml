<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="15dp"
    android:paddingRight="15dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:background="#F6F6F6">

    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundFrameLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        app:qmui_backgroundColor="#FFFFFF"
        app:qmui_radius="4dp">

        <TextView
            android:id="@+id/size_item_title"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="start"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="4dp"
            android:gravity="center_vertical"
            android:textColor="@android:color/black"
            android:textSize="14sp"
            tools:text="美国签证" />

        <TextView
            android:id="@+id/size_item_size"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="4dp"
            android:gravity="center_vertical"
            android:textColor="#777777"
            android:textSize="14sp"
            tools:text="35*45mm" />

    </com.qmuiteam.qmui.widget.roundwidget.QMUIRoundFrameLayout>

</FrameLayout>

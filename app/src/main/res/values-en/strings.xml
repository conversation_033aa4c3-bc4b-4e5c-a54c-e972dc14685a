<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="permission_request_denied">Permission request denied</string>
    <string name="error_gif">x or y bound size should be at least %1$dpx and file size should be no more than %2$sM</string>
    <string name="menu_scan_image">Scan</string>
    <string name="menu_history">History</string>
    <string name="menu_mine">My Profile</string>
    <string name="tab_home">Home</string>
    <string name="tab_fav">Tips</string>
    <string name="tab_mine">My Profile</string>
    <string name="tab_shop">Printing</string>
    <string name="tab_news">News</string>
    <string name="tab_scan_pic">Scan Photo</string>
    <string name="tab_scan_word">Precision</string>
    <string name="history" tools:ignore="ExtraTranslation">History</string>
    <string name="about_us">About Us</string>
    <string name="check_update">Check for Updates</string>
    <string name="feedback">Feedback</string>
    <string name="clear_cache">Clear Cache</string>
    <string name="private_policy">Privacy Policy</string>

    <string name="last_version">Already the latest version</string>
    <string name="no_market">No app store installed</string>
    <string name="clear_history">Cache cleared</string>
    <string name="qq">Contact us: <EMAIL></string>
    <string name="version">Current version: v</string>
    <string name="str_feedback_content">Please provide your valuable feedback</string>
    <string name="str_feedback_qq">Contact information (Facebook)</string>
    <string name="submit_tips">Your feedback content or contact information is empty</string>
    <string name="submit_success">Submitted successfully</string>
    <string name="submit">Submit</string>

    <string name="take_photo_skills">Photography Tips</string>
    <string name="tabSegment_item_1_title">Visa Photo Specifications</string>
    <string name="tabSegment_item_2_title">ID Photo Specifications</string>
    <string name="tabSegment_pre_1_title">Digital Photo</string>
    <string name="tabSegment_pre_2_title">Layout Photo</string>
    <string name="app_slogn">Smart Photography with Precise Image Recognition</string>
    <string name="again_info">We strictly protect users\' privacy information. If you do not agree to the policy, some functions of our app may be limited.</string>
    <string name="share_other">Recommend to Others</string>
    <string name="good_rate">Rate Us</string>
    <string name="content">This application respects and protects the personal privacy of all users. To provide you with more precise and personalized services, this application will use and disclose your personal information in accordance with the privacy policy.</string>


        <!-- General -->
        <string name="app_name">Plant Recognition</string>

        <!-- Top Bar -->
        <string name="home_title">Home</string>

        <!-- Photo Recognition Section -->
        <string name="take_photo_title">Camera Recognition</string>
        <string name="take_photo_desc">One-click photo \n recognition</string>

        <!-- Gallery Photo Recognition Section -->
        <string name="gallery_photo_title">Gallery Recognition</string>
        <string name="gallery_photo_desc">Recognize local \n images freely</string>

        <!-- Precise Recognition Title -->
        <string name="precise_recognition">Precise Recognition</string>

        <!-- Plant Recognition -->
        <string name="plant_recognition">Plant Recognition</string>
        <string name="plant_recognition_desc">Over 20,000 plant \n species</string>

        <!-- Animal Recognition -->
        <string name="animal_recognition">Animal Recognition</string>
        <string name="animal_recognition_desc">Over 8,000 animal \n species</string>

        <!-- Flower Recognition -->
        <string name="flower_recognition">Flower Recognition</string>
        <string name="flower_recognition_desc">Over 10,000 flower \n types</string>

        <!-- Fruit & Vegetable Recognition -->
        <string name="fruit_veg_recognition">Fruit &amp; Vegetable</string>
        <string name="fruit_veg_recognition_desc">Thousands of fruits \n and vegetables</string>

        <!-- Brand Logo Recognition -->
        <string name="logo_recognition">Brand Logo Recognition</string>
        <string name="logo_recognition_desc">Over 10,000 brand logos</string>

        <!-- Dish Recognition -->
        <string name="dish_recognition">Dish Recognition</string>
        <string name="dish_recognition_desc">Nearly 1,000 dishes</string>

        <!-- Landmark Recognition -->
        <string name="landmark_recognition">Landmark Recognition</string>
        <string name="landmark_recognition_desc">World-famous \n landmarks</string>

        <!-- Currency Recognition -->
        <string name="currency_recognition">Currency Recognition</string>
        <string name="currency_recognition_desc">Global currencies</string>

        <!-- Product Recognition -->
        <string name="product_recognition">Product Recognition</string>
        <string name="product_recognition_desc">Thousands of \n products</string>

        <!-- Text Recognition -->
        <string name="text_recognition">Text Recognition</string>
        <string name="text_recognition_desc">Accurate text \n extraction</string>
    <string name="history_empty_text">No history records</string>
    <string name="recognition_result">Recognition Result</string>
    <string name="choose_browser">Please select a browser</string>
    <string name="baidu_url">https://simple.m.wikipedia.org/wiki/%s</string>

    <string name="possible_recognition_result">The recognition result might be:</string>
    <string name="encyclopedia">Encyclopedia</string>

    <!-- New strings for Image2TxtResultAct -->
    <string name="copy_success">Copy successful!</string>

    <!-- Format strings for displaying recognition results -->
    <string name="bank_info_format">Bank: %1$s\nBank Card Number: %2$s</string>

    <string name="id_info_format">Name: %1$s\nID Number: %2$s\nGender: %3$s\nDate of Birth: %4$s\nEthnicity: %5$s\nAddress: %6$s</string>

    <string name="license_info_format">Address: %1$s\nPermitted Vehicle Types: %2$s\nValid Until: %3$s\nDate of Birth: %4$s\nLicense Number: %5$s\nAddress: %6$s\nFirst Issue Date: %7$s\nNationality: %8$s\nPermitted Vehicle Types: %9$s\nGender: %10$s\nValidity Period: %11$s</string>

    <!-- Album text -->
    <string name="album">Album</string>
    <!-- Flash text -->
    <string name="flash_on">Flash on</string>
    <string name="flash_off">Flash Off</string>


    <!-- Permission related messages -->
    <string name="permission_storage_denied">Please allow storage permission</string>
    <string name="permission_storage_rationale">Storage permission is required to select images. Allow?</string>
    <string name="permission_media_rationale">Media permission is required to access your photos. Allow?</string>
    <string name="permission_camera_denied">Please enable camera permission!</string>

    <!-- Dialog buttons -->
    <string name="cancel">Cancel</string>
    <string name="allow">Allow</string>
    <string name="confirm">Confirm</string>

    <!-- Exit Dialog -->
    <string name="exit_dialog_title">Notice</string>
    <string name="exit_dialog_message">Are you sure you want to exit?</string>
    <string name="exit_dialog_positive">Yes</string>


    <string name="share_app_message">Download it now from the app store!</string>
    <string name="share_app_title">Share App</string>
    <string name="cannot_open_store">Cannot open app store</string>



    <string name="service_agreement">Service Agreement</string>
    <string name="privacy_policy_title">Privacy Policy</string>
    <string name="read_more">Read</string>
    <string name="and">and</string>
    <string name="period">.</string>
    <string name="btn_cancel">Cancel</string>
    <string name="btn_agree">Agree</string>

    <string name="welcome_app">Welcome to APP</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="btn_agree_and_continue">Agree</string>
    <string name="btn_disagree_and_exit">Disagree and Exit</string>

</resources>
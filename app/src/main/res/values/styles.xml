<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="QMUI.Compat.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorWhite</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:statusBarColor">@color/colorWhite</item>
        <item name="android:windowLightStatusBar">true</item>

        <item name="QMUITopBarStyle">@style/QDTopBar</item>

        <item name="app_primary_color">@color/app_primary_color</item>
        <item name="app_content_bg_color">@color/app_content_bg_color</item>
    </style>

    <!-- ************启动页************ -->
    <style name="AppTheme.Splash" parent="QMUI.Compat.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="QDTopBar" parent="QMUI.TopBar">
        <item name="qmui_topbar_title_color">@color/qmui_config_color_white</item>
        <item name="qmui_topbar_subtitle_color">@color/qmui_config_color_white</item>
        <item name="qmui_topbar_text_btn_color_state_list">@color/s_topbar_btn_color</item>
        <item name="qmui_topbar_height">48dp</item>
        <item name="qmui_topbar_image_btn_height">48dp</item>
    </style>
    <style name="NavTextAppearance" parent="TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12sp</item>
    </style>

</resources>

apply plugin: 'com.android.library'

// Required by bintray
version = '2.2.0'
group = 'com.otaliastudios'


def travis = System.getenv("TRAVIS")

android {
    compileSdkVersion  33
    defaultConfig {
        minSdkVersion  21
        targetSdkVersion 33
        versionCode 1
        versionName project.version
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        if (travis) {
            testInstrumentationRunnerArgument "notAnnotation", "com.otaliastudios.cameraview.DoNotRunOnTravis"
        }
    }

    buildTypes {
        debug {
            testCoverageEnabled true
        }

        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    testImplementation 'junit:junit:4.12'
    testImplementation 'org.mockito:mockito-inline:2.28.2'

    androidTestImplementation 'androidx.test:runner:1.2.0'
    androidTestImplementation 'androidx.test:rules:1.2.0'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'org.mockito:mockito-android:2.28.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'

    api 'androidx.exifinterface:exifinterface:1.0.0'
    api 'androidx.lifecycle:lifecycle-common:2.1.0-alpha01'
    api 'com.google.android.gms:play-services-tasks:17.0.0'
    implementation 'androidx.annotation:annotation:1.1.0'
}






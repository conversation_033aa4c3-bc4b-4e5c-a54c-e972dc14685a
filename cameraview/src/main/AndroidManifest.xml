<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.otaliastudios.cameraview">

    <uses-permission android:name="android.permission.CAMERA" />

    <!-- Have developers add this. We don't want AUDIO permission to be auto-added to
         apps that just want to take pictures. -->
    <!-- uses-permission android:name="android.permission.RECORD_AUDIO" /-->

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false"/>
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false"/>
    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="false"/>
    <uses-feature
        android:name="android.hardware.microphone"
        android:required="false"/>

    <application/>

</manifest>
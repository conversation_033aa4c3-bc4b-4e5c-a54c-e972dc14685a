package com.otaliastudios.cameraview.video;

import android.graphics.SurfaceTexture;
import android.opengl.EGL14;
import android.os.Build;

import com.otaliastudios.cameraview.CameraLogger;
import com.otaliastudios.cameraview.internal.DeviceEncoders;
import com.otaliastudios.cameraview.overlay.Overlay;
import com.otaliastudios.cameraview.VideoResult;
import com.otaliastudios.cameraview.controls.Audio;
import com.otaliastudios.cameraview.engine.CameraEngine;
import com.otaliastudios.cameraview.overlay.OverlayDrawer;
import com.otaliastudios.cameraview.preview.GlCameraPreview;
import com.otaliastudios.cameraview.preview.RendererFrameCallback;
import com.otaliastudios.cameraview.preview.RendererThread;
import com.otaliastudios.cameraview.filter.Filter;
import com.otaliastudios.cameraview.size.Size;
import com.otaliastudios.cameraview.video.encoding.AudioConfig;
import com.otaliastudios.cameraview.video.encoding.AudioMediaEncoder;
import com.otaliastudios.cameraview.video.encoding.EncoderThread;
import com.otaliastudios.cameraview.video.encoding.MediaEncoderEngine;
import com.otaliastudios.cameraview.video.encoding.TextureConfig;
import com.otaliastudios.cameraview.video.encoding.TextureMediaEncoder;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

/**
 * A {@link VideoRecorder} that uses {@link android.media.MediaCodec} APIs.
 */
@RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
public class SnapshotVideoRecorder extends VideoRecorder implements RendererFrameCallback,
        MediaEncoderEngine.Listener {

    private static final String TAG = SnapshotVideoRecorder.class.getSimpleName();
    private static final CameraLogger LOG = CameraLogger.create(TAG);

    private static final int DEFAULT_VIDEO_FRAMERATE = 30;
    private static final int DEFAULT_AUDIO_BITRATE = 64000;

    // https://stackoverflow.com/a/5220554/4288782
    // Assuming low motion, we don't want to put this too high for default usage,
    // advanced users are still free to change this for each video.
    private static int estimateVideoBitRate(@NonNull Size size, int frameRate) {
        return (int) (0.07F * 1F * size.getWidth() * size.getHeight() * frameRate);
    }

    private static final int STATE_RECORDING = 0;
    private static final int STATE_NOT_RECORDING = 1;

    private MediaEncoderEngine mEncoderEngine;
    private GlCameraPreview mPreview;

    private int mCurrentState = STATE_NOT_RECORDING;
    private int mDesiredState = STATE_NOT_RECORDING;
    private int mTextureId = 0;

    private Overlay mOverlay;
    private OverlayDrawer mOverlayDrawer;
    private boolean mHasOverlay;
    private int mOverlayRotation;

    private Filter mCurrentFilter;

    public SnapshotVideoRecorder(@NonNull CameraEngine engine,
                                 @NonNull GlCameraPreview preview,
                                 @Nullable Overlay overlay,
                                 int overlayRotation) {
        super(engine);
        mPreview = preview;
        mOverlay = overlay;
        mHasOverlay = overlay != null && overlay.drawsOn(Overlay.Target.VIDEO_SNAPSHOT);
        mOverlayRotation = overlayRotation;
    }

    @Override
    protected void onStart() {
        mPreview.addRendererFrameCallback(this);
        mDesiredState = STATE_RECORDING;
    }

    @Override
    protected void onStop(boolean isCameraShutdown) {
        if (isCameraShutdown) {
            // The renderer callback might never be called. From my tests, it's not.
            LOG.i("Stopping the encoder engine from isCameraShutdown.");
            mDesiredState = STATE_NOT_RECORDING;
            mCurrentState = STATE_NOT_RECORDING;
            mEncoderEngine.stop();
        } else {
            mDesiredState = STATE_NOT_RECORDING;
        }
    }

    @RendererThread
    @Override
    public void onRendererTextureCreated(int textureId) {
        mTextureId = textureId;
        if (mHasOverlay) {
            mOverlayDrawer = new OverlayDrawer(mOverlay, mResult.size);
        }
    }

    @Override
    public void onRendererFilterChanged(@NonNull Filter filter) {
        mCurrentFilter = filter.copy();
        if (mEncoderEngine != null) {
            mCurrentFilter.setSize(mResult.size.getWidth(), mResult.size.getHeight());
            mEncoderEngine.notify(TextureMediaEncoder.FILTER_EVENT, mCurrentFilter);
        }
    }

    @RendererThread
    @Override
    public void onRendererFrame(@NonNull SurfaceTexture surfaceTexture, float scaleX, float scaleY) {
        if (mCurrentState == STATE_NOT_RECORDING && mDesiredState == STATE_RECORDING) {
            LOG.i("Starting the encoder engine.");

            // Set default options
            if (mResult.videoFrameRate <= 0) mResult.videoFrameRate = DEFAULT_VIDEO_FRAMERATE;
            if (mResult.videoBitRate <= 0) mResult.videoBitRate = estimateVideoBitRate(mResult.size, mResult.videoFrameRate);
            if (mResult.audioBitRate <= 0) mResult.audioBitRate = DEFAULT_AUDIO_BITRATE;

            // Define mime types
            String videoType = "";
            switch (mResult.videoCodec) {
                case H_263: videoType = "video/3gpp"; break; // MediaFormat.MIMETYPE_VIDEO_H263;
                case H_264: videoType = "video/avc"; break; // MediaFormat.MIMETYPE_VIDEO_AVC:
                case DEVICE_DEFAULT: videoType = "video/avc"; break;
            }
            String audioType = "audio/mp4a-latm";

            // Check the availability of values
            DeviceEncoders deviceEncoders = new DeviceEncoders(videoType, audioType, DeviceEncoders.MODE_PREFER_HARDWARE);
            mResult.size = deviceEncoders.getSupportedVideoSize(mResult.size);
            mResult.videoBitRate = deviceEncoders.getSupportedVideoBitRate(mResult.videoBitRate);
            mResult.audioBitRate = deviceEncoders.getSupportedAudioBitRate(mResult.audioBitRate);
            mResult.videoFrameRate = deviceEncoders.getSupportedVideoFrameRate(mResult.size, mResult.videoFrameRate);

            // Video
            TextureConfig videoConfig = new TextureConfig();
            videoConfig.width = mResult.size.getWidth();
            videoConfig.height = mResult.size.getHeight();
            videoConfig.bitRate = mResult.videoBitRate;
            videoConfig.frameRate = mResult.videoFrameRate;
            videoConfig.rotation = mResult.rotation;
            videoConfig.mimeType = videoType;
            videoConfig.encoder = deviceEncoders.getVideoEncoder();
            videoConfig.textureId = mTextureId;
            videoConfig.scaleX = scaleX;
            videoConfig.scaleY = scaleY;
            // Get egl context from the RendererThread, which is the one in which we have created
            // the textureId and the overlayTextureId, managed by the GlSurfaceView.
            // Next operations can then be performed on different threads using this handle.
            videoConfig.eglContext = EGL14.eglGetCurrentContext();
            if (mHasOverlay) {
                videoConfig.overlayTarget = Overlay.Target.VIDEO_SNAPSHOT;
                videoConfig.overlayDrawer = mOverlayDrawer;
                videoConfig.overlayRotation = mOverlayRotation;
            }
            TextureMediaEncoder videoEncoder = new TextureMediaEncoder(videoConfig);

            // Adjustment
            mResult.rotation = 0; // We will rotate the result instead.
            mCurrentFilter.setSize(mResult.size.getWidth(), mResult.size.getWidth());

            // Audio
            AudioMediaEncoder audioEncoder = null;
            if (mResult.audio == Audio.ON || mResult.audio == Audio.MONO || mResult.audio == Audio.STEREO) {
                AudioConfig audioConfig = new AudioConfig();
                audioConfig.bitRate = mResult.audioBitRate;
                if (mResult.audio == Audio.MONO) audioConfig.channels = 1;
                if (mResult.audio == Audio.STEREO) audioConfig.channels = 2;
                audioConfig.encoder = deviceEncoders.getAudioEncoder();
                audioEncoder = new AudioMediaEncoder(audioConfig);
            }

            // Engine
            mEncoderEngine = new MediaEncoderEngine(mResult.file,
                    videoEncoder,
                    audioEncoder,
                    mResult.maxDuration,
                    mResult.maxSize,
                    SnapshotVideoRecorder.this);
            mEncoderEngine.notify(TextureMediaEncoder.FILTER_EVENT, mCurrentFilter);
            mEncoderEngine.start();
            mCurrentState = STATE_RECORDING;
        }

        if (mCurrentState == STATE_RECORDING) {
            LOG.v("dispatching frame.");
            TextureMediaEncoder textureEncoder = (TextureMediaEncoder) mEncoderEngine.getVideoEncoder();
            TextureMediaEncoder.Frame frame = textureEncoder.acquireFrame();
            frame.timestampNanos = surfaceTexture.getTimestamp();
            frame.timestampMillis = System.currentTimeMillis(); // NOTE: this is an approximation but it seems to work.
            surfaceTexture.getTransformMatrix(frame.transform);
            if (mEncoderEngine != null) { // Can happen on teardown. At least it used to.
                mEncoderEngine.notify(TextureMediaEncoder.FRAME_EVENT, frame);
            }
        }

        if (mCurrentState == STATE_RECORDING && mDesiredState == STATE_NOT_RECORDING) {
            LOG.i("Stopping the encoder engine.");
            mCurrentState = STATE_NOT_RECORDING;
            mEncoderEngine.stop();
        }

    }

    @Override
    public void onEncodingStart() {
        dispatchVideoRecordingStart();
    }

    @Override
    public void onEncodingStop() {
        dispatchVideoRecordingEnd();
    }

    @EncoderThread
    @Override
    public void onEncodingEnd(int stopReason, @Nullable Exception e) {
        // If something failed, undo the result, since this is the mechanism
        // to notify Camera1Engine about this.
        if (e != null) {
            LOG.e("Error onEncodingEnd", e);
            mResult = null;
            mError = e;
        } else {
            if (stopReason == MediaEncoderEngine.END_BY_MAX_DURATION) {
                LOG.i("onEncodingEnd because of max duration.");
                mResult.endReason = VideoResult.REASON_MAX_DURATION_REACHED;
            } else if (stopReason == MediaEncoderEngine.END_BY_MAX_SIZE) {
                LOG.i("onEncodingEnd because of max size.");
                mResult.endReason = VideoResult.REASON_MAX_SIZE_REACHED;
            } else {
                LOG.i("onEncodingEnd because of user.");
            }
        }
        // Cleanup
        mCurrentState = STATE_NOT_RECORDING;
        mDesiredState = STATE_NOT_RECORDING;
        mPreview.removeRendererFrameCallback(SnapshotVideoRecorder.this);
        mPreview = null;
        if (mOverlayDrawer != null) {
            mOverlayDrawer.release();
            mOverlayDrawer = null;
        }
        mEncoderEngine = null;
        dispatchResult();
    }
}

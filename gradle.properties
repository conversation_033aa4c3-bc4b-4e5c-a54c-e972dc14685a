# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enable configuration cache
org.gradle.configuration-cache=true
# Enable Gradle Daemon
org.gradle.daemon=true
# Enable caching
org.gradle.caching=true
# Suppress unsupported compileSdk warning
android.suppressUnsupportedCompileSdk=34

okhttp_version=4.9.1
rxlife_version=2.1.0
rxhttp_version=2.6.2
rxhttp_next_version=2.6.3

fastjson_version=1.2.76
jackson_version=2.12.3
moshi_version=1.12.0
protobuf_version=3.11.4
simple_xml_version=2.7.1
